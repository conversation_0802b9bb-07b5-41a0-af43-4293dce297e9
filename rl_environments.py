# -*- coding: utf-8 -*-
"""
Reinforcement Learning Environments for Pro Trader RL
强化学习环境模块：包含Buy Knowledge RL和Sell Knowledge RL环境
"""

import numpy as np
import pandas as pd
import gym
from gym import spaces
from typing import Dict, List, Tuple, Optional, Any
import logging
from abc import ABC, abstractmethod
from config import *
from data_preprocessing import TradingDataGenerator

class BaseRLEnvironment(gym.Env, ABC):
    """强化学习环境基类"""
    
    def __init__(self, data_generator: TradingDataGenerator, config: Dict):
        super().__init__()
        self.data_generator = data_generator
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 环境状态
        self.current_step = 0
        self.done = False
        self.info = {}
        
        # 性能统计
        self.episode_rewards = []
        self.episode_actions = []
        self.episode_returns = []
        
    @abstractmethod
    def reset(self):
        """重置环境"""
        pass
    
    @abstractmethod
    def step(self, action):
        """执行动作"""
        pass
    
    @abstractmethod
    def _calculate_reward(self, action, actual_return):
        """计算奖励"""
        pass
    
    def _get_info(self):
        """获取环境信息"""
        return {
            'episode_length': self.current_step,
            'total_reward': sum(self.episode_rewards),
            'average_reward': np.mean(self.episode_rewards) if self.episode_rewards else 0,
            'action_distribution': np.bincount(self.episode_actions) if self.episode_actions else [],
        }

class BuyKnowledgeRLEnvironment(BaseRLEnvironment):
    """
    Buy Knowledge RL Environment
    模拟专业交易员的买入决策过程
    """
    
    def __init__(self, data_generator: TradingDataGenerator):
        config = ENV_CONFIG['buy_env']
        super().__init__(data_generator, config)
        
        # 动作空间：[>=10%概率, <10%概率]
        self.action_space = spaces.Discrete(2)
        
        # 状态空间：标准化后的因子
        n_features = len(SELECTED_FACTORS)
        self.observation_space = spaces.Box(
            low=-np.inf, high=np.inf, 
            shape=(n_features,), dtype=np.float32
        )
        
        # 环境特定参数
        self.success_threshold = ENV_CONFIG['success_threshold']  # 10%
        self.positive_reward = config['positive_reward']
        self.negative_reward = config['negative_reward']
        
        # 当前批次数据
        self.current_batch = None
        self.batch_index = 0
        
        self.logger.info(f"Buy Knowledge RL环境初始化完成")
        self.logger.info(f"动作空间: {self.action_space}")
        self.logger.info(f"状态空间: {self.observation_space.shape}")
    
    def reset(self):
        """重置环境，获取新的数据批次"""
        self.current_step = 0
        self.batch_index = 0
        self.done = False
        self.episode_rewards = []
        self.episode_actions = []
        self.episode_returns = []
        
        # 获取新的数据批次
        self.current_batch = self.data_generator.get_batch(batch_size=64)
        
        if self.current_batch is None:
            # 数据用完，重置数据生成器
            self.data_generator.reset()
            self.current_batch = self.data_generator.get_batch(batch_size=64)
        
        if self.current_batch is None:
            # 仍然没有数据，返回零状态
            return np.zeros(self.observation_space.shape[0], dtype=np.float32)
        
        # 返回第一个观测
        observation = self.current_batch['features'][0].astype(np.float32)
        return observation
    
    def step(self, action):
        """
        执行动作并返回结果
        Args:
            action: 0表示预测<10%, 1表示预测>=10%
        Returns:
            observation, reward, done, info
        """
        if self.current_batch is None or self.batch_index >= len(self.current_batch['features']):
            # 当前批次结束，获取新批次
            self.current_batch = self.data_generator.get_batch(batch_size=64)
            self.batch_index = 0
            
            if self.current_batch is None:
                # 没有更多数据，episode结束
                self.done = True
                return (np.zeros(self.observation_space.shape[0], dtype=np.float32), 
                       0.0, True, self._get_info())
        
        # 获取当前状态的实际收益率
        actual_return = self.current_batch['returns'][self.batch_index]
        
        # 计算奖励
        reward = self._calculate_reward(action, actual_return)
        
        # 记录统计信息
        self.episode_rewards.append(reward)
        self.episode_actions.append(action)
        self.episode_returns.append(actual_return)
        
        # 移动到下一个样本
        self.batch_index += 1
        self.current_step += 1
        
        # 获取下一个观测
        if self.batch_index < len(self.current_batch['features']):
            next_observation = self.current_batch['features'][self.batch_index].astype(np.float32)
        else:
            # 当前批次结束，但还没有获取新批次
            next_observation = np.zeros(self.observation_space.shape[0], dtype=np.float32)
        
        # 检查是否结束episode
        if self.current_step >= TRAINING_CONFIG['max_episode_length']:
            self.done = True
        
        return next_observation, reward, self.done, self._get_info()
    
    def _calculate_reward(self, action, actual_return):
        """
        计算奖励 - 基于论文中的奖励函数设计
        
        奖励场景：
        - 场景1：预测>=10%且实际>=10% -> +1分
        - 场景2：预测>=10%且实际<10% -> 0分  
        - 场景3：预测<10%且实际<10% -> +1分
        - 场景4：预测<10%且实际>=10% -> 0分
        """
        is_success = actual_return >= self.success_threshold
        predict_success = action == 1  # action=1表示预测>=10%
        
        if predict_success and is_success:
            # 场景1：正确预测成功
            reward = self.positive_reward
        elif predict_success and not is_success:
            # 场景2：错误预测成功
            reward = self.negative_reward
        elif not predict_success and not is_success:
            # 场景3：正确预测失败
            reward = self.positive_reward
        else:
            # 场景4：错误预测失败
            reward = self.negative_reward
        
        return reward
    
    def get_stock_selection(self, predictions: np.ndarray, 
                          ts_codes: np.ndarray, 
                          max_stocks: int = 10) -> List[str]:
        """
        根据预测结果选择股票（测试模式使用）
        Args:
            predictions: 预测概率数组 [N, 2]
            ts_codes: 股票代码数组
            max_stocks: 最大选择股票数
        Returns:
            选中的股票代码列表
        """
        # 获取预测>=10%的概率
        success_probs = predictions[:, 1]
        
        # 按概率排序，选择前max_stocks只股票
        sorted_indices = np.argsort(success_probs)[::-1]
        selected_indices = sorted_indices[:max_stocks]
        
        selected_stocks = [ts_codes[i] for i in selected_indices 
                          if success_probs[i] > 0.5]  # 只选择概率>0.5的
        
        return selected_stocks[:max_stocks]

class SellKnowledgeRLEnvironment(BaseRLEnvironment):
    """
    Sell Knowledge RL Environment  
    模拟专业交易员的卖出决策过程
    """
    
    def __init__(self, data_generator: TradingDataGenerator):
        config = ENV_CONFIG['sell_env']
        super().__init__(data_generator, config)
        
        # 动作空间：[卖出概率, 持有概率]
        self.action_space = spaces.Discrete(2)
        
        # 状态空间：标准化后的因子 + 当前收益率
        n_features = len(SELECTED_FACTORS) + 1  # +1 for current return
        self.observation_space = spaces.Box(
            low=-np.inf, high=np.inf,
            shape=(n_features,), dtype=np.float32
        )
        
        # 环境特定参数
        self.hold_period = config['hold_period']  # 120天
        self.max_reward = config['max_reward']
        self.min_reward = config['min_reward']
        self.success_threshold = ENV_CONFIG['success_threshold']
        
        # 当前交易状态
        self.current_position = None
        self.buy_date = None
        self.buy_price = None
        self.holding_days = 0
        self.position_history = []
        
        self.logger.info(f"Sell Knowledge RL环境初始化完成")
        self.logger.info(f"最大持有期: {self.hold_period}天")
    
    def reset(self):
        """重置环境，开始新的交易序列"""
        self.current_step = 0
        self.done = False
        self.episode_rewards = []
        self.episode_actions = []
        self.episode_returns = []
        
        # 重置交易状态
        self.current_position = None
        self.buy_date = None
        self.buy_price = None
        self.holding_days = 0
        self.position_history = []
        
        # 获取一个买入信号作为起点
        batch = self.data_generator.get_batch(batch_size=1)
        if batch is None:
            self.data_generator.reset()
            batch = self.data_generator.get_batch(batch_size=1)
        
        if batch is None:
            return np.zeros(self.observation_space.shape[0], dtype=np.float32)
        
        # 设置初始位置
        self.current_position = {
            'ts_code': batch['ts_codes'][0],
            'buy_date': batch['trade_dates'][0],
            'buy_price': batch['prices']['open'][0],
            'features': batch['features'][0]
        }
        
        self.buy_date = self.current_position['buy_date']
        self.buy_price = self.current_position['buy_price']
        self.holding_days = 0
        
        # 构建初始观测（特征 + 当前收益率0）
        observation = np.concatenate([
            self.current_position['features'],
            [0.0]  # 初始收益率为0
        ]).astype(np.float32)
        
        return observation
    
    def step(self, action):
        """
        执行卖出决策
        Args:
            action: 0表示持有, 1表示卖出
        Returns:
            observation, reward, done, info
        """
        if self.current_position is None:
            # 没有持仓，重置环境
            return self.reset(), 0.0, True, self._get_info()
        
        self.holding_days += 1
        self.current_step += 1
        
        # 获取当前股票的历史数据
        ts_code = self.current_position['ts_code']
        current_date = pd.to_datetime(self.buy_date) + pd.Timedelta(days=self.holding_days)
        
        stock_history = self.data_generator.get_stock_history(
            ts_code, current_date, window=self.hold_period
        )
        
        if stock_history is None or len(stock_history) == 0:
            # 没有更多数据，强制结束
            self.done = True
            return (np.zeros(self.observation_space.shape[0], dtype=np.float32),
                   0.0, True, self._get_info())
        
        # 获取当前价格和收益率
        current_data = stock_history.iloc[-1]
        current_price = current_data['close']
        current_return = (current_price - self.buy_price) / self.buy_price
        
        # 记录位置历史
        self.position_history.append({
            'day': self.holding_days,
            'price': current_price,
            'return': current_return,
            'action': action
        })
        
        # 计算奖励
        reward = 0.0
        
        if action == 1:  # 卖出
            reward = self._calculate_sell_reward(current_return)
            self.done = True
            self.logger.debug(f"卖出决策: 持有{self.holding_days}天, 收益率{current_return:.4f}, 奖励{reward:.4f}")
            
        elif self.holding_days >= self.hold_period:  # 达到最大持有期
            reward = self._calculate_sell_reward(current_return)
            self.done = True
            self.logger.debug(f"达到最大持有期: {self.hold_period}天, 收益率{current_return:.4f}")
            
        else:  # 继续持有
            reward = self._calculate_hold_reward(current_return)
        
        # 记录统计信息
        self.episode_rewards.append(reward)
        self.episode_actions.append(action)
        self.episode_returns.append(current_return)
        
        # 构建下一个观测
        if not self.done and len(stock_history) > self.holding_days:
            # 获取下一天的特征
            next_features = stock_history.iloc[-1][SELECTED_FACTORS].fillna(0).values
            next_observation = np.concatenate([
                next_features,
                [current_return]
            ]).astype(np.float32)
        else:
            next_observation = np.zeros(self.observation_space.shape[0], dtype=np.float32)
        
        return next_observation, reward, self.done, self._get_info()
    
    def _calculate_sell_reward(self, current_return):
        """
        计算卖出奖励 - 基于论文中的相对奖励机制
        
        对于收益率>=10%的情况：
        - 最高收益获得+2分
        - 最低收益获得+1分
        - 使用相对排名计算奖励
        
        对于收益率<10%的情况：
        - 给予-1分惩罚
        """
        if current_return >= self.success_threshold:
            # 成功交易，计算相对奖励
            # 这里简化处理，实际应该基于所有成功交易的排名
            # 收益率越高，奖励越高
            normalized_return = min(current_return / 0.5, 1.0)  # 假设50%为满分
            reward = 1.0 + normalized_return  # 1.0 到 2.0之间
        else:
            # 失败交易
            reward = self.min_reward
        
        return reward
    
    def _calculate_hold_reward(self, current_return):
        """
        计算持有奖励
        
        持有期间的奖励：
        - 如果当前收益<10%，给予小额正奖励鼓励持有
        - 如果当前收益>=10%但选择持有，给予小额负奖励
        """
        if current_return < self.success_threshold:
            # 收益不足，鼓励继续持有
            reward = 0.1
        else:
            # 收益已达标但选择持有，轻微惩罚
            reward = -0.1
        
        return reward
    
    def _calculate_reward(self, action, actual_return):
        """基类方法实现"""
        if action == 1:  # 卖出
            return self._calculate_sell_reward(actual_return)
        else:  # 持有
            return self._calculate_hold_reward(actual_return)
    
    def get_optimal_sell_time(self, predictions: np.ndarray, 
                            threshold: float = 0.85) -> Optional[int]:
        """
        确定最优卖出时机（测试模式使用）
        Args:
            predictions: 120天的预测概率 [120, 2]
            threshold: 卖出阈值
        Returns:
            最优卖出日期（相对于买入日期的天数）
        """
        sell_probs = predictions[:, 1]  # 卖出概率
        hold_probs = predictions[:, 0]  # 持有概率
        
        # 找到卖出概率-持有概率差值>threshold且卖出概率>持有概率的第一天
        prob_diff = sell_probs - hold_probs
        
        for day in range(len(predictions)):
            if prob_diff[day] > threshold and sell_probs[day] > hold_probs[day]:
                return day + 1  # 返回天数（从1开始）
        
        # 如果没有找到合适的卖出时机，返回None（持有到期）
        return None

class StopLossRule:
    """
    止损规则模块 - 基于规则的风险管理
    """
    
    def __init__(self):
        self.config = STOP_LOSS_CONFIG
        self.logger = logging.getLogger(__name__)
        
        # 止损参数
        self.stop_loss_threshold = self.config['stop_loss_threshold']  # -10%
        self.sideways_days = self.config['sideways_days']  # 20天
        self.sideways_return_threshold = self.config['sideways_return_threshold']  # 10%
        self.sideways_check_window = self.config['sideways_check_window']  # 120天
        
        # 动态止损参数
        self.use_dynamic_stop_loss = self.config['use_dynamic_stop_loss']
        self.trailing_stop_pct = self.config['trailing_stop_pct']  # 5%
        self.volatility_multiplier = self.config['volatility_multiplier']  # 2.0
        
        self.logger.info("止损规则模块初始化完成")
    
    def check_stop_loss_on_dips(self, current_return: float) -> bool:
        """
        检查跌幅止损
        Args:
            current_return: 当前收益率
        Returns:
            是否触发止损
        """
        if current_return <= self.stop_loss_threshold:
            self.logger.info(f"触发跌幅止损: 当前收益率{current_return:.4f} <= 阈值{self.stop_loss_threshold}")
            return True
        return False
    
    def check_stop_loss_on_sideways(self, return_history: List[float]) -> bool:
        """
        检查横盘止损
        Args:
            return_history: 历史收益率列表
        Returns:
            是否触发止损
        """
        if len(return_history) < self.sideways_days:
            return False
        
        # 检查最近sideways_days天内收益率都<10%的天数
        recent_returns = return_history[-self.sideways_check_window:]
        low_return_days = sum(1 for r in recent_returns if r < self.sideways_return_threshold)
        
        if low_return_days >= self.sideways_days:
            self.logger.info(f"触发横盘止损: {low_return_days}天收益率<{self.sideways_return_threshold}")
            return True
        
        return False
    
    def check_dynamic_stop_loss(self, current_return: float, 
                              max_return: float, 
                              volatility: float) -> bool:
        """
        检查动态止损（跟踪止损）
        Args:
            current_return: 当前收益率
            max_return: 历史最高收益率
            volatility: 收益率波动率
        Returns:
            是否触发止损
        """
        if not self.use_dynamic_stop_loss:
            return False
        
        # 计算动态止损线
        trailing_stop = max_return - self.trailing_stop_pct
        volatility_stop = max_return - volatility * self.volatility_multiplier
        
        # 使用更严格的止损线
        dynamic_stop = max(trailing_stop, volatility_stop)
        
        if current_return <= dynamic_stop:
            self.logger.info(f"触发动态止损: 当前收益率{current_return:.4f} <= 动态止损线{dynamic_stop:.4f}")
            return True
        
        return False
    
    def should_stop_loss(self, current_return: float, 
                        return_history: List[float],
                        max_return: float = None,
                        volatility: float = None) -> Tuple[bool, str]:
        """
        综合判断是否应该止损
        Args:
            current_return: 当前收益率
            return_history: 历史收益率
            max_return: 历史最高收益率
            volatility: 收益率波动率
        Returns:
            (是否止损, 止损原因)
        """
        # 检查跌幅止损
        if self.check_stop_loss_on_dips(current_return):
            return True, "跌幅止损"
        
        # 检查横盘止损
        if self.check_stop_loss_on_sideways(return_history):
            return True, "横盘止损"
        
        # 检查动态止损
        if max_return is not None and volatility is not None:
            if self.check_dynamic_stop_loss(current_return, max_return, volatility):
                return True, "动态止损"
        
        return False, ""

def create_buy_knowledge_env(data_generator: TradingDataGenerator) -> BuyKnowledgeRLEnvironment:
    """创建Buy Knowledge RL环境"""
    return BuyKnowledgeRLEnvironment(data_generator)

def create_sell_knowledge_env(data_generator: TradingDataGenerator) -> SellKnowledgeRLEnvironment:
    """创建Sell Knowledge RL环境"""
    return SellKnowledgeRLEnvironment(data_generator)

def main():
    """测试RL环境"""
    import logging
    from data_preprocessing import DataPreprocessor, TradingDataGenerator
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建数据预处理器和数据生成器
    preprocessor = DataPreprocessor()
    data_dict = preprocessor.load_data()
    
    if 'stock_factors' not in data_dict:
        print("⚠️  需要先运行因子计算")
        return
    
    train_data = preprocessor.prepare_training_data(
        data_dict, TRAIN_START_DATE, TRAIN_END_DATE, mode='train'
    )
    
    if train_data['processed_data'].empty:
        print("⚠️  训练数据为空")
        return
    
    data_generator = TradingDataGenerator(train_data['processed_data'])
    
    # 测试Buy Knowledge环境
    print("\n=== 测试Buy Knowledge RL环境 ===")
    buy_env = create_buy_knowledge_env(data_generator)
    
    obs = buy_env.reset()
    print(f"初始观测形状: {obs.shape}")
    
    for step in range(5):
        action = buy_env.action_space.sample()  # 随机动作
        obs, reward, done, info = buy_env.step(action)
        print(f"步骤{step}: 动作={action}, 奖励={reward:.4f}, 结束={done}")
        
        if done:
            break
    
    # 测试Sell Knowledge环境
    print("\n=== 测试Sell Knowledge RL环境 ===")
    sell_env = create_sell_knowledge_env(data_generator)
    
    obs = sell_env.reset()
    print(f"初始观测形状: {obs.shape}")
    
    for step in range(10):
        action = sell_env.action_space.sample()  # 随机动作
        obs, reward, done, info = sell_env.step(action)
        print(f"步骤{step}: 动作={action}, 奖励={reward:.4f}, 结束={done}")
        
        if done:
            break
    
    # 测试止损规则
    print("\n=== 测试止损规则 ===")
    stop_loss = StopLossRule()
    
    # 测试跌幅止损
    should_stop, reason = stop_loss.should_stop_loss(-0.12, [])
    print(f"跌幅-12%: 止损={should_stop}, 原因={reason}")
    
    # 测试横盘止损
    sideways_returns = [0.05] * 25  # 25天都是5%收益
    should_stop, reason = stop_loss.should_stop_loss(0.05, sideways_returns)
    print(f"横盘25天: 止损={should_stop}, 原因={reason}")

if __name__ == "__main__":
    main()
