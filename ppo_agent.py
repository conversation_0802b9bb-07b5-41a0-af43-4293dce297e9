# -*- coding: utf-8 -*-
"""
PPO Agent Module for Pro Trader RL
PPO智能体模块：实现Buy Knowledge RL和Sell Knowledge RL的PPO算法
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.distributions import Categorical
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from collections import deque
import os
from config import *

class ActorCriticNetwork(nn.Module):
    """Actor-Critic网络架构"""
    
    def __init__(self, state_dim: int, action_dim: int, config: Dict):
        super(ActorCriticNetwork, self).__init__()
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.config = config
        
        # Actor网络层
        actor_layers = config['actor_layers']
        self.actor_layers = nn.ModuleList()
        
        for i in range(len(actor_layers) - 1):
            layer = nn.Linear(actor_layers[i], actor_layers[i + 1])
            self.actor_layers.append(layer)
            
            # 权重初始化
            if config['weight_init'] == 'xavier_uniform':
                nn.init.xavier_uniform_(layer.weight)
            elif config['weight_init'] == 'kaiming_normal':
                nn.init.kaiming_normal_(layer.weight)
            
            if config['bias_init'] == 'zeros':
                nn.init.zeros_(layer.bias)
        
        # Critic网络层
        critic_layers = config['critic_layers']
        self.critic_layers = nn.ModuleList()
        
        for i in range(len(critic_layers) - 1):
            layer = nn.Linear(critic_layers[i], critic_layers[i + 1])
            self.critic_layers.append(layer)
            
            # 权重初始化
            if config['weight_init'] == 'xavier_uniform':
                nn.init.xavier_uniform_(layer.weight)
            elif config['weight_init'] == 'kaiming_normal':
                nn.init.kaiming_normal_(layer.weight)
            
            if config['bias_init'] == 'zeros':
                nn.init.zeros_(layer.bias)
        
        # 激活函数
        if config['actor_activation'] == 'relu':
            self.actor_activation = F.relu
        elif config['actor_activation'] == 'tanh':
            self.actor_activation = torch.tanh
        elif config['actor_activation'] == 'leaky_relu':
            self.actor_activation = F.leaky_relu
        
        if config['critic_activation'] == 'relu':
            self.critic_activation = F.relu
        elif config['critic_activation'] == 'tanh':
            self.critic_activation = torch.tanh
        elif config['critic_activation'] == 'leaky_relu':
            self.critic_activation = F.leaky_relu
    
    def forward(self, state):
        """前向传播"""
        # Actor网络
        actor_x = state
        for i, layer in enumerate(self.actor_layers[:-1]):
            actor_x = self.actor_activation(layer(actor_x))
        
        # 输出层
        if self.config['actor_output_activation'] == 'softmax':
            action_probs = F.softmax(self.actor_layers[-1](actor_x), dim=-1)
        else:
            action_probs = self.actor_layers[-1](actor_x)
        
        # Critic网络
        critic_x = state
        for i, layer in enumerate(self.critic_layers[:-1]):
            critic_x = self.critic_activation(layer(critic_x))
        
        # 价值输出
        state_value = self.critic_layers[-1](critic_x)
        
        return action_probs, state_value
    
    def get_action_and_value(self, state, action=None):
        """获取动作和价值"""
        action_probs, state_value = self.forward(state)
        
        # 创建分布
        dist = Categorical(action_probs)
        
        if action is None:
            action = dist.sample()
        
        action_logprob = dist.log_prob(action)
        dist_entropy = dist.entropy()
        
        return action, action_logprob, dist_entropy, state_value

class PPOBuffer:
    """PPO经验回放缓冲区"""
    
    def __init__(self, buffer_size: int, state_dim: int, device: torch.device):
        self.buffer_size = buffer_size
        self.state_dim = state_dim
        self.device = device
        
        # 缓冲区
        self.states = torch.zeros((buffer_size, state_dim), dtype=torch.float32, device=device)
        self.actions = torch.zeros(buffer_size, dtype=torch.long, device=device)
        self.rewards = torch.zeros(buffer_size, dtype=torch.float32, device=device)
        self.values = torch.zeros(buffer_size, dtype=torch.float32, device=device)
        self.logprobs = torch.zeros(buffer_size, dtype=torch.float32, device=device)
        self.dones = torch.zeros(buffer_size, dtype=torch.bool, device=device)
        
        self.ptr = 0
        self.size = 0
    
    def store(self, state, action, reward, value, logprob, done):
        """存储经验"""
        self.states[self.ptr] = torch.FloatTensor(state).to(self.device)
        self.actions[self.ptr] = action
        self.rewards[self.ptr] = reward
        self.values[self.ptr] = value
        self.logprobs[self.ptr] = logprob
        self.dones[self.ptr] = done
        
        self.ptr = (self.ptr + 1) % self.buffer_size
        self.size = min(self.size + 1, self.buffer_size)
    
    def get_batch(self):
        """获取所有经验"""
        indices = torch.arange(self.size, device=self.device)
        
        return (
            self.states[:self.size],
            self.actions[:self.size],
            self.rewards[:self.size],
            self.values[:self.size],
            self.logprobs[:self.size],
            self.dones[:self.size]
        )
    
    def clear(self):
        """清空缓冲区"""
        self.ptr = 0
        self.size = 0

class PPOAgent:
    """PPO智能体"""
    
    def __init__(self, state_dim: int, action_dim: int, config: Dict, device: torch.device = None):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.config = config
        self.device = device if device else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # PPO超参数
        self.learning_rate = PPO_CONFIG['learning_rate']
        self.gamma = PPO_CONFIG['gamma']
        self.gae_lambda = PPO_CONFIG['gae_lambda']
        self.clip_range = PPO_CONFIG['clip_range']
        self.value_coef = PPO_CONFIG['value_coef']
        self.entropy_coef = PPO_CONFIG['entropy_coef']
        self.max_grad_norm = PPO_CONFIG['max_grad_norm']
        self.n_epochs = PPO_CONFIG['n_epochs']
        self.batch_size = PPO_CONFIG['batch_size']
        
        # 网络
        self.network = ActorCriticNetwork(state_dim, action_dim, config).to(self.device)
        self.optimizer = optim.Adam(self.network.parameters(), lr=self.learning_rate)
        
        # 经验缓冲区
        self.buffer = PPOBuffer(PPO_CONFIG['n_steps'], state_dim, self.device)
        
        # 训练统计
        self.training_step = 0
        self.episode_count = 0
        self.total_rewards = []
        self.policy_losses = []
        self.value_losses = []
        self.entropy_losses = []
        
        self.logger.info(f"PPO智能体初始化完成 - 设备: {self.device}")
        self.logger.info(f"网络架构 - Actor: {config['actor_layers']}, Critic: {config['critic_layers']}")
    
    def get_action(self, state: np.ndarray, deterministic: bool = False) -> Tuple[int, float, float, float]:
        """
        获取动作
        Args:
            state: 状态
            deterministic: 是否确定性选择动作
        Returns:
            (action, action_logprob, entropy, state_value)
        """
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            
            if deterministic:
                action_probs, state_value = self.network(state_tensor)
                action = torch.argmax(action_probs, dim=-1)
                
                dist = Categorical(action_probs)
                action_logprob = dist.log_prob(action)
                entropy = dist.entropy()
            else:
                action, action_logprob, entropy, state_value = self.network.get_action_and_value(state_tensor)
            
            return (
                action.cpu().numpy()[0],
                action_logprob.cpu().numpy()[0],
                entropy.cpu().numpy()[0],
                state_value.cpu().numpy()[0]
            )
    
    def store_experience(self, state, action, reward, value, logprob, done):
        """存储经验到缓冲区"""
        self.buffer.store(state, action, reward, value, logprob, done)
    
    def compute_gae(self, rewards, values, dones, next_value=0):
        """计算广义优势估计(GAE)"""
        advantages = torch.zeros_like(rewards)
        returns = torch.zeros_like(rewards)
        
        gae = 0
        for step in reversed(range(len(rewards))):
            if step == len(rewards) - 1:
                next_non_terminal = 1.0 - dones[step]
                next_value_step = next_value
            else:
                next_non_terminal = 1.0 - dones[step + 1]
                next_value_step = values[step + 1]
            
            delta = rewards[step] + self.gamma * next_value_step * next_non_terminal - values[step]
            gae = delta + self.gamma * self.gae_lambda * next_non_terminal * gae
            advantages[step] = gae
            returns[step] = gae + values[step]
        
        return advantages, returns
    
    def update(self, next_value=0):
        """更新网络参数"""
        if self.buffer.size < self.batch_size:
            return
        
        # 获取经验
        states, actions, rewards, values, old_logprobs, dones = self.buffer.get_batch()
        
        # 计算优势和回报
        with torch.no_grad():
            advantages, returns = self.compute_gae(rewards, values, dones, next_value)
            
            # 标准化优势
            advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        # 多轮更新
        total_policy_loss = 0
        total_value_loss = 0
        total_entropy_loss = 0
        
        for epoch in range(self.n_epochs):
            # 随机打乱数据
            indices = torch.randperm(self.buffer.size, device=self.device)
            
            for start in range(0, self.buffer.size, self.batch_size):
                end = start + self.batch_size
                batch_indices = indices[start:end]
                
                if len(batch_indices) < self.batch_size:
                    continue
                
                # 获取批次数据
                batch_states = states[batch_indices]
                batch_actions = actions[batch_indices]
                batch_advantages = advantages[batch_indices]
                batch_returns = returns[batch_indices]
                batch_old_logprobs = old_logprobs[batch_indices]
                
                # 前向传播
                _, new_logprobs, entropy, new_values = self.network.get_action_and_value(
                    batch_states, batch_actions
                )
                
                # 计算比率
                ratio = torch.exp(new_logprobs - batch_old_logprobs)
                
                # PPO损失
                surr1 = ratio * batch_advantages
                surr2 = torch.clamp(ratio, 1 - self.clip_range, 1 + self.clip_range) * batch_advantages
                policy_loss = -torch.min(surr1, surr2).mean()
                
                # 价值损失
                value_loss = F.mse_loss(new_values.squeeze(), batch_returns)
                
                # 熵损失
                entropy_loss = -entropy.mean()
                
                # 总损失
                total_loss = (
                    policy_loss + 
                    self.value_coef * value_loss + 
                    self.entropy_coef * entropy_loss
                )
                
                # 反向传播
                self.optimizer.zero_grad()
                total_loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.network.parameters(), self.max_grad_norm)
                
                self.optimizer.step()
                
                # 记录损失
                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
                total_entropy_loss += entropy_loss.item()
        
        # 记录平均损失
        n_updates = self.n_epochs * (self.buffer.size // self.batch_size)
        if n_updates > 0:
            self.policy_losses.append(total_policy_loss / n_updates)
            self.value_losses.append(total_value_loss / n_updates)
            self.entropy_losses.append(total_entropy_loss / n_updates)
        
        # 清空缓冲区
        self.buffer.clear()
        self.training_step += 1
        
        self.logger.debug(f"更新完成 - 策略损失: {self.policy_losses[-1]:.4f}, "
                         f"价值损失: {self.value_losses[-1]:.4f}, "
                         f"熵损失: {self.entropy_losses[-1]:.4f}")
    
    def save_model(self, filepath: str):
        """保存模型"""
        torch.save({
            'network_state_dict': self.network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'training_step': self.training_step,
            'episode_count': self.episode_count,
            'config': self.config
        }, filepath)
        self.logger.info(f"模型已保存到: {filepath}")
    
    def load_model(self, filepath: str):
        """加载模型"""
        if not os.path.exists(filepath):
            self.logger.warning(f"模型文件不存在: {filepath}")
            return False
        
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.network.load_state_dict(checkpoint['network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.training_step = checkpoint.get('training_step', 0)
        self.episode_count = checkpoint.get('episode_count', 0)
        
        self.logger.info(f"模型已从 {filepath} 加载")
        return True
    
    def get_training_stats(self) -> Dict:
        """获取训练统计信息"""
        return {
            'training_step': self.training_step,
            'episode_count': self.episode_count,
            'avg_policy_loss': np.mean(self.policy_losses[-100:]) if self.policy_losses else 0,
            'avg_value_loss': np.mean(self.value_losses[-100:]) if self.value_losses else 0,
            'avg_entropy_loss': np.mean(self.entropy_losses[-100:]) if self.entropy_losses else 0,
            'avg_reward': np.mean(self.total_rewards[-100:]) if self.total_rewards else 0
        }

class BuyKnowledgeAgent(PPOAgent):
    """Buy Knowledge RL智能体"""
    
    def __init__(self, device: torch.device = None):
        config = BUY_KNOWLEDGE_CONFIG
        state_dim = config['state_dim']
        action_dim = config['action_dim']
        
        super().__init__(state_dim, action_dim, config, device)
        self.logger = logging.getLogger('BuyKnowledgeAgent')
        
        self.logger.info("Buy Knowledge RL智能体初始化完成")
        self.logger.info(f"状态维度: {state_dim}, 动作维度: {action_dim}")
    
    def predict_batch(self, states: np.ndarray, deterministic: bool = True) -> np.ndarray:
        """
        批量预测
        Args:
            states: 状态批次 [N, state_dim]
            deterministic: 是否确定性预测
        Returns:
            动作概率 [N, action_dim]
        """
        with torch.no_grad():
            states_tensor = torch.FloatTensor(states).to(self.device)
            action_probs, _ = self.network(states_tensor)
            return action_probs.cpu().numpy()
    
    def select_stocks(self, predictions: np.ndarray, ts_codes: List[str], 
                     max_stocks: int = 10) -> List[str]:
        """
        根据预测结果选择股票
        Args:
            predictions: 预测概率 [N, 2]
            ts_codes: 股票代码列表
            max_stocks: 最大选择数量
        Returns:
            选中的股票代码列表
        """
        # 获取预测成功(>=10%)的概率
        success_probs = predictions[:, 1]
        
        # 按概率排序
        sorted_indices = np.argsort(success_probs)[::-1]
        
        # 选择概率最高的股票
        selected_stocks = []
        for idx in sorted_indices:
            if len(selected_stocks) >= max_stocks:
                break
            if success_probs[idx] > 0.5:  # 只选择概率>50%的股票
                selected_stocks.append(ts_codes[idx])
        
        return selected_stocks

class SellKnowledgeAgent(PPOAgent):
    """Sell Knowledge RL智能体"""
    
    def __init__(self, device: torch.device = None):
        config = SELL_KNOWLEDGE_CONFIG
        state_dim = config['state_dim']
        action_dim = config['action_dim']
        
        super().__init__(state_dim, action_dim, config, device)
        self.logger = logging.getLogger('SellKnowledgeAgent')
        
        self.logger.info("Sell Knowledge RL智能体初始化完成")
        self.logger.info(f"状态维度: {state_dim}, 动作维度: {action_dim}")
    
    def predict_sequence(self, states: np.ndarray, deterministic: bool = True) -> np.ndarray:
        """
        序列预测（用于120天的卖出决策）
        Args:
            states: 状态序列 [T, state_dim]
            deterministic: 是否确定性预测
        Returns:
            动作概率序列 [T, action_dim]
        """
        with torch.no_grad():
            states_tensor = torch.FloatTensor(states).to(self.device)
            action_probs, _ = self.network(states_tensor)
            return action_probs.cpu().numpy()
    
    def find_optimal_sell_time(self, predictions: np.ndarray, 
                              threshold: float = 0.85) -> Optional[int]:
        """
        找到最优卖出时机
        Args:
            predictions: 预测概率序列 [T, 2]
            threshold: 卖出阈值
        Returns:
            最优卖出日期（从0开始）
        """
        sell_probs = predictions[:, 1]  # 卖出概率
        hold_probs = predictions[:, 0]  # 持有概率
        
        # 找到卖出概率-持有概率差值>threshold且卖出概率>持有概率的第一天
        prob_diff = sell_probs - hold_probs
        
        for day in range(len(predictions)):
            if prob_diff[day] > threshold and sell_probs[day] > hold_probs[day]:
                return day
        
        return None  # 没有找到合适的卖出时机

def create_buy_knowledge_agent(device: torch.device = None) -> BuyKnowledgeAgent:
    """创建Buy Knowledge智能体"""
    return BuyKnowledgeAgent(device)

def create_sell_knowledge_agent(device: torch.device = None) -> SellKnowledgeAgent:
    """创建Sell Knowledge智能体"""
    return SellKnowledgeAgent(device)

def main():
    """测试PPO智能体"""
    import logging
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 测试Buy Knowledge智能体
    print("\n=== 测试Buy Knowledge智能体 ===")
    buy_agent = create_buy_knowledge_agent(device)
    
    # 测试动作选择
    dummy_state = np.random.randn(len(SELECTED_FACTORS))
    action, logprob, entropy, value = buy_agent.get_action(dummy_state)
    print(f"动作: {action}, 对数概率: {logprob:.4f}, 熵: {entropy:.4f}, 价值: {value:.4f}")
    
    # 测试批量预测
    dummy_states = np.random.randn(10, len(SELECTED_FACTORS))
    predictions = buy_agent.predict_batch(dummy_states)
    print(f"批量预测形状: {predictions.shape}")
    print(f"预测概率示例: {predictions[0]}")
    
    # 测试股票选择
    ts_codes = [f"00000{i}.SZ" for i in range(10)]
    selected = buy_agent.select_stocks(predictions, ts_codes, max_stocks=5)
    print(f"选中股票: {selected}")
    
    # 测试Sell Knowledge智能体
    print("\n=== 测试Sell Knowledge智能体 ===")
    sell_agent = create_sell_knowledge_agent(device)
    
    # 测试序列预测
    dummy_sequence = np.random.randn(120, len(SELECTED_FACTORS) + 1)  # +1 for current return
    sequence_predictions = sell_agent.predict_sequence(dummy_sequence)
    print(f"序列预测形状: {sequence_predictions.shape}")
    
    # 测试最优卖出时机
    optimal_day = sell_agent.find_optimal_sell_time(sequence_predictions)
    print(f"最优卖出日期: {optimal_day}")
    
    # 测试模型保存和加载
    print("\n=== 测试模型保存和加载 ===")
    save_path = "test_model.pth"
    buy_agent.save_model(save_path)
    
    # 创建新智能体并加载模型
    new_agent = create_buy_knowledge_agent(device)
    success = new_agent.load_model(save_path)
    print(f"模型加载成功: {success}")
    
    # 清理测试文件
    if os.path.exists(save_path):
        os.remove(save_path)
    
    print("\n✅ PPO智能体测试完成")

if __name__ == "__main__":
    main()
