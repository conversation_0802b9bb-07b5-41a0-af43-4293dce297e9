# -*- coding: utf-8 -*-
"""
Trading Strategy Module for Pro Trader RL
交易策略模块：实现完整的交易策略和回测系统
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import logging
from datetime import datetime, timedelta
import os
from dataclasses import dataclass
from config import *
from data_preprocessing import DataPreprocessor, TradingDataGenerator
from ppo_agent import create_buy_knowledge_agent, create_sell_knowledge_agent
from rl_environments import StopLossRule

@dataclass
class Trade:
    """交易记录"""
    ts_code: str
    buy_date: str
    sell_date: str
    buy_price: float
    sell_price: float
    quantity: int
    return_rate: float
    holding_days: int
    trade_type: str  # 'RL', 'Stop_Loss'
    
class Portfolio:
    """投资组合管理"""
    
    def __init__(self, initial_capital: float, max_positions: int, max_position_pct: float, 
                 transaction_fee: float):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.max_positions = max_positions
        self.max_position_pct = max_position_pct
        self.transaction_fee = transaction_fee
        
        # 持仓记录
        self.positions = {}  # {ts_code: {'quantity': int, 'avg_price': float, 'buy_date': str}}
        self.available_cash = initial_capital
        
        # 交易记录
        self.trade_history = []
        self.daily_values = []
        
        self.logger = logging.getLogger(__name__)
    
    def can_buy(self, ts_code: str, price: float) -> bool:
        """判断是否可以买入"""
        # 检查是否已持仓
        if ts_code in self.positions:
            return False
        
        # 检查持仓数量限制
        if len(self.positions) >= self.max_positions:
            return False
        
        # 检查资金是否足够
        max_investment = self.current_capital * self.max_position_pct
        min_quantity = 100  # 最小买入手数
        required_cash = price * min_quantity * (1 + self.transaction_fee)
        
        return self.available_cash >= required_cash and self.available_cash >= max_investment
    
    def buy_stock(self, ts_code: str, price: float, date: str) -> bool:
        """买入股票"""
        if not self.can_buy(ts_code, price):
            return False
        
        # 计算买入数量
        max_investment = self.current_capital * self.max_position_pct
        affordable_quantity = int(self.available_cash / (price * (1 + self.transaction_fee)))
        max_quantity = int(max_investment / (price * (1 + self.transaction_fee)))
        
        # 取最小值，并确保是100的倍数
        quantity = min(affordable_quantity, max_quantity)
        quantity = (quantity // 100) * 100
        
        if quantity < 100:
            return False
        
        # 计算实际成本
        cost = quantity * price * (1 + self.transaction_fee)
        
        # 更新持仓和资金
        self.positions[ts_code] = {
            'quantity': quantity,
            'avg_price': price,
            'buy_date': date
        }
        self.available_cash -= cost
        
        self.logger.debug(f"买入 {ts_code}: {quantity}股 @ {price:.2f}, 成本: {cost:.2f}")
        return True
    
    def sell_stock(self, ts_code: str, price: float, date: str, trade_type: str = 'RL') -> bool:
        """卖出股票"""
        if ts_code not in self.positions:
            return False
        
        position = self.positions[ts_code]
        quantity = position['quantity']
        buy_price = position['avg_price']
        buy_date = position['buy_date']
        
        # 计算收益
        proceeds = quantity * price * (1 - self.transaction_fee)
        self.available_cash += proceeds
        
        # 计算收益率和持有天数
        return_rate = (price - buy_price) / buy_price
        holding_days = (pd.to_datetime(date) - pd.to_datetime(buy_date)).days
        
        # 记录交易
        trade = Trade(
            ts_code=ts_code,
            buy_date=buy_date,
            sell_date=date,
            buy_price=buy_price,
            sell_price=price,
            quantity=quantity,
            return_rate=return_rate,
            holding_days=holding_days,
            trade_type=trade_type
        )
        self.trade_history.append(trade)
        
        # 删除持仓
        del self.positions[ts_code]
        
        self.logger.debug(f"卖出 {ts_code}: {quantity}股 @ {price:.2f}, "
                         f"收益率: {return_rate:.4f}, 持有: {holding_days}天")
        return True
    
    def update_portfolio_value(self, date: str, price_data: Dict[str, float]):
        """更新投资组合价值"""
        position_value = 0
        
        for ts_code, position in self.positions.items():
            if ts_code in price_data:
                current_price = price_data[ts_code]
                position_value += position['quantity'] * current_price
        
        total_value = self.available_cash + position_value
        self.current_capital = total_value
        
        self.daily_values.append({
            'date': date,
            'cash': self.available_cash,
            'positions_value': position_value,
            'total_value': total_value,
            'positions_count': len(self.positions)
        })
    
    def get_performance_metrics(self) -> Dict:
        """计算投资组合绩效指标"""
        if not self.trade_history:
            return {}
        
        # 基本统计
        total_trades = len(self.trade_history)
        winning_trades = sum(1 for trade in self.trade_history if trade.return_rate > 0)
        losing_trades = total_trades - winning_trades
        
        # 收益率统计
        returns = [trade.return_rate for trade in self.trade_history]
        avg_return = np.mean(returns)
        std_return = np.std(returns)
        
        # 胜率
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 平均持有天数
        avg_holding_days = np.mean([trade.holding_days for trade in self.trade_history])
        
        # 总收益率
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital
        
        # 年化收益率（假设252个交易日）
        if self.daily_values:
            total_days = (pd.to_datetime(self.daily_values[-1]['date']) - 
                         pd.to_datetime(self.daily_values[0]['date'])).days
            annual_return = (1 + total_return) ** (252 / max(total_days, 1)) - 1
        else:
            annual_return = 0
        
        # 最大回撤
        if self.daily_values:
            values = [record['total_value'] for record in self.daily_values]
            peak = np.maximum.accumulate(values)
            drawdown = (peak - values) / peak
            max_drawdown = np.max(drawdown)
        else:
            max_drawdown = 0
        
        # 夏普比率（假设无风险收益率为0）
        sharpe_ratio = avg_return / std_return if std_return > 0 else 0
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'avg_return_per_trade': avg_return,
            'avg_holding_days': avg_holding_days,
            'final_capital': self.current_capital
        }

class ProTraderRLStrategy:
    """Pro Trader RL完整交易策略"""
    
    def __init__(self, buy_model_path: str, sell_model_path: str):
        self.logger = logging.getLogger(__name__)
        
        # 加载训练好的模型
        self.buy_agent = create_buy_knowledge_agent()
        self.sell_agent = create_sell_knowledge_agent()
        
        if not self.buy_agent.load_model(buy_model_path):
            raise ValueError(f"无法加载Buy Knowledge模型: {buy_model_path}")
        
        if not self.sell_agent.load_model(sell_model_path):
            raise ValueError(f"无法加载Sell Knowledge模型: {sell_model_path}")
        
        # 止损规则
        self.stop_loss_rule = StopLossRule()
        
        # 数据预处理器
        self.preprocessor = DataPreprocessor()
        
        # 投资组合
        self.portfolio = Portfolio(
            initial_capital=BACKTEST_CONFIG['initial_capital'],
            max_positions=ENV_CONFIG['max_positions'],
            max_position_pct=ENV_CONFIG['max_position_pct'],
            transaction_fee=BACKTEST_CONFIG['commission_rate']
        )
        
        self.logger.info("Pro Trader RL策略初始化完成")
    
    def generate_buy_signals(self, data: pd.DataFrame, date: str) -> List[str]:
        """生成买入信号"""
        # 筛选当日可买入的股票
        current_data = data[data['trade_date'] == date].copy()
        
        if current_data.empty:
            return []
        
        # 准备特征数据
        features = []
        ts_codes = []
        
        for _, row in current_data.iterrows():
            # 提取特征
            feature_vector = []
            for factor in SELECTED_FACTORS:
                value = row.get(factor, 0)
                if pd.isna(value):
                    value = 0
                feature_vector.append(value)
            
            features.append(feature_vector)
            ts_codes.append(row['ts_code'])
        
        if not features:
            return []
        
        features = np.array(features)
        
        # 使用Buy Knowledge RL进行预测
        predictions = self.buy_agent.predict_batch(features, deterministic=True)
        
        # 选择股票
        selected_stocks = self.buy_agent.select_stocks(
            predictions, ts_codes, max_stocks=self.portfolio.max_positions
        )
        
        self.logger.debug(f"{date}: 生成买入信号 {len(selected_stocks)} 只股票")
        return selected_stocks
    
    def generate_sell_signals(self, data: pd.DataFrame, date: str) -> List[Tuple[str, str]]:
        """生成卖出信号"""
        sell_signals = []
        
        for ts_code, position in self.portfolio.positions.items():
            # 获取持仓历史数据
            stock_data = data[data['ts_code'] == ts_code].copy()
            stock_data = stock_data[stock_data['trade_date'] <= date].sort_values('trade_date')
            
            if len(stock_data) == 0:
                continue
            
            # 计算持有天数和当前收益率
            buy_date = pd.to_datetime(position['buy_date'])
            current_date = pd.to_datetime(date)
            holding_days = (current_date - buy_date).days
            
            current_price = stock_data.iloc[-1]['close']
            current_return = (current_price - position['avg_price']) / position['avg_price']
            
            # 检查止损条件
            return_history = []
            for i in range(max(0, len(stock_data) - 120), len(stock_data)):
                price = stock_data.iloc[i]['close']
                ret = (price - position['avg_price']) / position['avg_price']
                return_history.append(ret)
            
            should_stop, stop_reason = self.stop_loss_rule.should_stop_loss(
                current_return, return_history, 
                max(return_history) if return_history else current_return,
                np.std(return_history) if len(return_history) > 1 else 0
            )
            
            if should_stop:
                sell_signals.append((ts_code, f"Stop_Loss_{stop_reason}"))
                self.logger.debug(f"{date}: {ts_code} 触发止损 - {stop_reason}")
                continue
            
            # 达到最大持有期限
            if holding_days >= ENV_CONFIG['sell_env']['hold_period']:
                sell_signals.append((ts_code, "Max_Holding_Period"))
                self.logger.debug(f"{date}: {ts_code} 达到最大持有期")
                continue
            
            # 使用Sell Knowledge RL进行决策
            # 构建状态特征（因子 + 当前收益率）
            latest_row = stock_data.iloc[-1]
            feature_vector = []
            
            for factor in SELECTED_FACTORS:
                value = latest_row.get(factor, 0)
                if pd.isna(value):
                    value = 0
                feature_vector.append(value)
            
            feature_vector.append(current_return)  # 添加当前收益率
            
            # 预测
            action, _, _, _ = self.sell_agent.get_action(np.array(feature_vector), deterministic=True)
            
            if action == 1:  # 卖出
                sell_signals.append((ts_code, "RL_Sell"))
                self.logger.debug(f"{date}: {ts_code} RL决策卖出")
        
        return sell_signals
    
    def backtest(self, start_date: str, end_date: str) -> Dict:
        """执行回测"""
        self.logger.info(f"开始回测: {start_date} - {end_date}")
        
        # 加载测试数据
        data_dict = self.preprocessor.load_data()
        test_data = self.preprocessor.prepare_training_data(
            data_dict, start_date, end_date, mode='test'
        )
        
        if test_data['processed_data'].empty:
            raise ValueError("测试数据为空")
        
        data = test_data['processed_data']
        
        # 获取所有交易日期
        trading_dates = sorted(data['trade_date'].dt.strftime('%Y%m%d').unique())
        
        self.logger.info(f"回测期间共 {len(trading_dates)} 个交易日")
        
        for date in trading_dates:
            date_str = pd.to_datetime(date).strftime('%Y-%m-%d')
            
            # 更新投资组合价值
            current_prices = {}
            daily_data = data[data['trade_date'] == date]
            for _, row in daily_data.iterrows():
                current_prices[row['ts_code']] = row['close']
            
            self.portfolio.update_portfolio_value(date_str, current_prices)
            
            # 处理卖出信号
            sell_signals = self.generate_sell_signals(data, date)
            for ts_code, signal_type in sell_signals:
                if ts_code in current_prices:
                    sell_price = current_prices[ts_code]
                    trade_type = signal_type.replace('_', ' ')
                    self.portfolio.sell_stock(ts_code, sell_price, date_str, trade_type)
            
            # 处理买入信号
            buy_signals = self.generate_buy_signals(data, date)
            for ts_code in buy_signals:
                if ts_code in current_prices and self.portfolio.can_buy(ts_code, current_prices[ts_code]):
                    buy_price = current_prices[ts_code]
                    self.portfolio.buy_stock(ts_code, buy_price, date_str)
        
        # 计算最终绩效
        performance = self.portfolio.get_performance_metrics()
        
        self.logger.info("回测完成")
        self.logger.info(f"总收益率: {performance.get('total_return', 0):.4f}")
        self.logger.info(f"年化收益率: {performance.get('annual_return', 0):.4f}")
        self.logger.info(f"胜率: {performance.get('win_rate', 0):.4f}")
        self.logger.info(f"夏普比率: {performance.get('sharpe_ratio', 0):.4f}")
        self.logger.info(f"最大回撤: {performance.get('max_drawdown', 0):.4f}")
        
        return {
            'performance': performance,
            'trade_history': self.portfolio.trade_history,
            'daily_values': self.portfolio.daily_values,
            'final_positions': self.portfolio.positions
        }
    
    def save_backtest_results(self, results: Dict, output_dir: str):
        """保存回测结果"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存交易记录
        if results['trade_history']:
            trades_df = pd.DataFrame([
                {
                    'ts_code': trade.ts_code,
                    'buy_date': trade.buy_date,
                    'sell_date': trade.sell_date,
                    'buy_price': trade.buy_price,
                    'sell_price': trade.sell_price,
                    'quantity': trade.quantity,
                    'return_rate': trade.return_rate,
                    'holding_days': trade.holding_days,
                    'trade_type': trade.trade_type
                }
                for trade in results['trade_history']
            ])
            
            trades_file = os.path.join(output_dir, 'trade_history.csv')
            trades_df.to_csv(trades_file, index=False, encoding='utf-8')
            self.logger.info(f"交易记录已保存到: {trades_file}")
        
        # 保存每日净值
        if results['daily_values']:
            daily_df = pd.DataFrame(results['daily_values'])
            daily_file = os.path.join(output_dir, 'daily_values.csv')
            daily_df.to_csv(daily_file, index=False, encoding='utf-8')
            self.logger.info(f"每日净值已保存到: {daily_file}")
        
        # 保存绩效指标
        performance_file = os.path.join(output_dir, 'performance_metrics.json')
        import json
        with open(performance_file, 'w', encoding='utf-8') as f:
            json.dump(results['performance'], f, indent=2, ensure_ascii=False)
        self.logger.info(f"绩效指标已保存到: {performance_file}")

def run_backtest():
    """运行回测"""
    print("🔄 开始Pro Trader RL策略回测")
    print("="*60)
    
    # 模型路径
    buy_model_path = os.path.join(MODEL_SAVE_DIR, 'buy_knowledge', 'best_model.pth')
    sell_model_path = os.path.join(MODEL_SAVE_DIR, 'sell_knowledge', 'best_model.pth')
    
    # 检查模型文件是否存在
    if not os.path.exists(buy_model_path):
        print(f"❌ Buy Knowledge模型不存在: {buy_model_path}")
        print("请先运行训练程序")
        return
    
    if not os.path.exists(sell_model_path):
        print(f"❌ Sell Knowledge模型不存在: {sell_model_path}")
        print("请先运行训练程序")
        return
    
    try:
        # 创建策略
        strategy = ProTraderRLStrategy(buy_model_path, sell_model_path)
        
        # 执行回测
        results = strategy.backtest(TEST_START_DATE, TEST_END_DATE)
        
        # 保存结果
        output_dir = os.path.join(RESULTS_DIR, f"backtest_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        strategy.save_backtest_results(results, output_dir)
        
        print("\n✅ 回测完成！")
        print(f"📊 结果保存在: {output_dir}")
        print("="*60)
        
        # 打印关键指标
        perf = results['performance']
        print(f"📈 总收益率: {perf.get('total_return', 0):.2%}")
        print(f"📈 年化收益率: {perf.get('annual_return', 0):.2%}")
        print(f"🎯 胜率: {perf.get('win_rate', 0):.2%}")
        print(f"📊 夏普比率: {perf.get('sharpe_ratio', 0):.4f}")
        print(f"📉 最大回撤: {perf.get('max_drawdown', 0):.2%}")
        print(f"💰 最终资金: {perf.get('final_capital', 0):.2f}")
        print(f"🔄 总交易次数: {perf.get('total_trades', 0)}")
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    run_backtest()
