# -*- coding: utf-8 -*-
"""
Training Module for Pro Trader RL
训练模块：实现Buy Knowledge RL和Sell Knowledge RL的训练流程
"""

import os
import time
import logging
import numpy as np
import pandas as pd
import torch
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import matplotlib.pyplot as plt
from tqdm import tqdm
import json

from config import *
from data_preprocessing import DataPreprocessor, TradingDataGenerator
from rl_environments import create_buy_knowledge_env, create_sell_knowledge_env, StopLossRule
from ppo_agent import create_buy_knowledge_agent, create_sell_knowledge_agent

class TrainingLogger:
    """训练日志记录器"""
    
    def __init__(self, log_dir: str, experiment_name: str):
        self.log_dir = log_dir
        self.experiment_name = experiment_name
        self.log_file = os.path.join(log_dir, f"{experiment_name}.log")
        
        # 创建日志目录
        os.makedirs(log_dir, exist_ok=True)
        
        # 设置日志
        self.logger = logging.getLogger(experiment_name)
        self.logger.setLevel(logging.INFO)
        
        # 文件处理器
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(LOGGING_CONFIG['log_format'])
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        if not self.logger.handlers:
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
        
        # 训练指标存储
        self.metrics = {
            'buy_knowledge': {
                'episode_rewards': [],
                'episode_lengths': [],
                'policy_losses': [],
                'value_losses': [],
                'accuracy': [],
                'training_time': []
            },
            'sell_knowledge': {
                'episode_rewards': [],
                'episode_lengths': [],
                'policy_losses': [],
                'value_losses': [],
                'avg_holding_days': [],
                'success_rate': [],
                'training_time': []
            }
        }
    
    def log_episode(self, agent_type: str, episode: int, metrics: Dict):
        """记录episode指标"""
        self.logger.info(f"[{agent_type}] Episode {episode}: {metrics}")
        
        # 存储指标
        if agent_type in self.metrics:
            for key, value in metrics.items():
                if key in self.metrics[agent_type]:
                    self.metrics[agent_type][key].append(value)
    
    def save_metrics(self):
        """保存训练指标"""
        metrics_file = os.path.join(self.log_dir, f"{self.experiment_name}_metrics.json")
        with open(metrics_file, 'w', encoding='utf-8') as f:
            json.dump(self.metrics, f, indent=2, ensure_ascii=False)
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle(f'Training Curves - {self.experiment_name}')
        
        # Buy Knowledge RL曲线
        buy_metrics = self.metrics['buy_knowledge']
        
        if buy_metrics['episode_rewards']:
            axes[0, 0].plot(buy_metrics['episode_rewards'])
            axes[0, 0].set_title('Buy Knowledge - Episode Rewards')
            axes[0, 0].set_xlabel('Episode')
            axes[0, 0].set_ylabel('Reward')
        
        if buy_metrics['policy_losses']:
            axes[0, 1].plot(buy_metrics['policy_losses'])
            axes[0, 1].set_title('Buy Knowledge - Policy Loss')
            axes[0, 1].set_xlabel('Update')
            axes[0, 1].set_ylabel('Loss')
        
        if buy_metrics['accuracy']:
            axes[0, 2].plot(buy_metrics['accuracy'])
            axes[0, 2].set_title('Buy Knowledge - Accuracy')
            axes[0, 2].set_xlabel('Episode')
            axes[0, 2].set_ylabel('Accuracy')
        
        # Sell Knowledge RL曲线
        sell_metrics = self.metrics['sell_knowledge']
        
        if sell_metrics['episode_rewards']:
            axes[1, 0].plot(sell_metrics['episode_rewards'])
            axes[1, 0].set_title('Sell Knowledge - Episode Rewards')
            axes[1, 0].set_xlabel('Episode')
            axes[1, 0].set_ylabel('Reward')
        
        if sell_metrics['policy_losses']:
            axes[1, 1].plot(sell_metrics['policy_losses'])
            axes[1, 1].set_title('Sell Knowledge - Policy Loss')
            axes[1, 1].set_xlabel('Update')
            axes[1, 1].set_ylabel('Loss')
        
        if sell_metrics['success_rate']:
            axes[1, 2].plot(sell_metrics['success_rate'])
            axes[1, 2].set_title('Sell Knowledge - Success Rate')
            axes[1, 2].set_xlabel('Episode')
            axes[1, 2].set_ylabel('Success Rate')
        
        plt.tight_layout()
        plot_file = os.path.join(self.log_dir, f"{self.experiment_name}_curves.png")
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"训练曲线已保存到: {plot_file}")

class BuyKnowledgeTrainer:
    """Buy Knowledge RL训练器"""
    
    def __init__(self, data_generator: TradingDataGenerator, logger: TrainingLogger):
        self.data_generator = data_generator
        self.logger = logger
        
        # 创建环境和智能体
        self.env = create_buy_knowledge_env(data_generator)
        self.agent = create_buy_knowledge_agent()
        
        # 训练配置
        self.config = TRAINING_CONFIG
        self.max_episodes = self.config['max_episodes']
        self.save_freq = self.config['save_freq']
        self.eval_freq = self.config['eval_freq']
        
        # 早停配置
        self.early_stopping_patience = self.config['early_stopping_patience']
        self.early_stopping_threshold = self.config['early_stopping_threshold']
        self.best_reward = -np.inf
        self.patience_counter = 0
        
        # 模型保存路径
        self.model_save_path = os.path.join(MODEL_SAVE_DIR, 'buy_knowledge')
        os.makedirs(self.model_save_path, exist_ok=True)
        
        self.logger.logger.info("Buy Knowledge训练器初始化完成")
    
    def train_episode(self) -> Dict:
        """训练一个episode"""
        episode_start_time = time.time()
        
        # 重置环境
        state = self.env.reset()
        episode_reward = 0
        episode_length = 0
        correct_predictions = 0
        total_predictions = 0
        
        done = False
        while not done:
            # 获取动作
            action, logprob, entropy, value = self.agent.get_action(state)
            
            # 执行动作
            next_state, reward, done, info = self.env.step(action)
            
            # 存储经验
            self.agent.store_experience(state, action, reward, value, logprob, done)
            
            # 更新统计
            episode_reward += reward
            episode_length += 1
            
            # 计算准确率
            if hasattr(self.env, 'current_batch') and self.env.current_batch is not None:
                if self.env.batch_index > 0:
                    actual_return = self.env.current_batch['returns'][self.env.batch_index - 1]
                    is_success = actual_return >= ENV_CONFIG['success_threshold']
                    predict_success = action == 1
                    
                    if (predict_success and is_success) or (not predict_success and not is_success):
                        correct_predictions += 1
                    total_predictions += 1
            
            state = next_state
            
            # 检查是否需要更新网络
            if self.agent.buffer.size >= PPO_CONFIG['n_steps']:
                self.agent.update()
        
        # 最终更新
        if self.agent.buffer.size > 0:
            self.agent.update()
        
        # 计算指标
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        training_time = time.time() - episode_start_time
        
        return {
            'episode_reward': episode_reward,
            'episode_length': episode_length,
            'accuracy': accuracy,
            'training_time': training_time,
            'total_predictions': total_predictions
        }
    
    def evaluate(self, n_episodes: int = 10) -> Dict:
        """评估模型性能"""
        total_reward = 0
        total_accuracy = 0
        
        for _ in range(n_episodes):
            state = self.env.reset()
            episode_reward = 0
            correct_predictions = 0
            total_predictions = 0
            
            done = False
            while not done:
                # 确定性动作选择
                action, _, _, _ = self.agent.get_action(state, deterministic=True)
                next_state, reward, done, _ = self.env.step(action)
                
                episode_reward += reward
                
                # 计算准确率
                if hasattr(self.env, 'current_batch') and self.env.current_batch is not None:
                    if self.env.batch_index > 0:
                        actual_return = self.env.current_batch['returns'][self.env.batch_index - 1]
                        is_success = actual_return >= ENV_CONFIG['success_threshold']
                        predict_success = action == 1
                        
                        if (predict_success and is_success) or (not predict_success and not is_success):
                            correct_predictions += 1
                        total_predictions += 1
                
                state = next_state
            
            total_reward += episode_reward
            if total_predictions > 0:
                total_accuracy += correct_predictions / total_predictions
        
        return {
            'avg_reward': total_reward / n_episodes,
            'avg_accuracy': total_accuracy / n_episodes
        }
    
    def train(self) -> bool:
        """执行训练"""
        self.logger.logger.info("开始Buy Knowledge RL训练")
        
        for episode in tqdm(range(self.max_episodes), desc="Buy Knowledge Training"):
            # 训练一个episode
            metrics = self.train_episode()
            
            # 记录指标
            self.logger.log_episode('buy_knowledge', episode, metrics)
            
            # 更新智能体统计
            self.agent.episode_count += 1
            self.agent.total_rewards.append(metrics['episode_reward'])
            
            # 早停检查
            if metrics['episode_reward'] > self.best_reward + self.early_stopping_threshold:
                self.best_reward = metrics['episode_reward']
                self.patience_counter = 0
                
                # 保存最佳模型
                best_model_path = os.path.join(self.model_save_path, 'best_model.pth')
                self.agent.save_model(best_model_path)
            else:
                self.patience_counter += 1
            
            # 定期保存
            if (episode + 1) % self.save_freq == 0:
                model_path = os.path.join(self.model_save_path, f'model_episode_{episode + 1}.pth')
                self.agent.save_model(model_path)
            
            # 定期评估
            if (episode + 1) % self.eval_freq == 0:
                eval_metrics = self.evaluate()
                self.logger.logger.info(f"评估结果 - Episode {episode + 1}: {eval_metrics}")
            
            # 早停
            if self.patience_counter >= self.early_stopping_patience:
                self.logger.logger.info(f"早停触发 - Episode {episode + 1}")
                break
        
        # 保存最终模型
        final_model_path = os.path.join(self.model_save_path, 'final_model.pth')
        self.agent.save_model(final_model_path)
        
        self.logger.logger.info("Buy Knowledge RL训练完成")
        return True

class SellKnowledgeTrainer:
    """Sell Knowledge RL训练器"""
    
    def __init__(self, data_generator: TradingDataGenerator, logger: TrainingLogger):
        self.data_generator = data_generator
        self.logger = logger
        
        # 创建环境和智能体
        self.env = create_sell_knowledge_env(data_generator)
        self.agent = create_sell_knowledge_agent()
        
        # 训练配置
        self.config = TRAINING_CONFIG
        self.max_episodes = self.config['max_episodes']
        self.save_freq = self.config['save_freq']
        self.eval_freq = self.config['eval_freq']
        
        # 早停配置
        self.early_stopping_patience = self.config['early_stopping_patience']
        self.early_stopping_threshold = self.config['early_stopping_threshold']
        self.best_reward = -np.inf
        self.patience_counter = 0
        
        # 模型保存路径
        self.model_save_path = os.path.join(MODEL_SAVE_DIR, 'sell_knowledge')
        os.makedirs(self.model_save_path, exist_ok=True)
        
        self.logger.logger.info("Sell Knowledge训练器初始化完成")
    
    def train_episode(self) -> Dict:
        """训练一个episode"""
        episode_start_time = time.time()
        
        # 重置环境
        state = self.env.reset()
        episode_reward = 0
        episode_length = 0
        holding_days = 0
        final_return = 0
        
        done = False
        while not done:
            # 获取动作
            action, logprob, entropy, value = self.agent.get_action(state)
            
            # 执行动作
            next_state, reward, done, info = self.env.step(action)
            
            # 存储经验
            self.agent.store_experience(state, action, reward, value, logprob, done)
            
            # 更新统计
            episode_reward += reward
            episode_length += 1
            holding_days = self.env.holding_days
            
            # 获取最终收益率
            if hasattr(self.env, 'position_history') and self.env.position_history:
                final_return = self.env.position_history[-1]['return']
            
            state = next_state
            
            # 检查是否需要更新网络
            if self.agent.buffer.size >= PPO_CONFIG['n_steps']:
                self.agent.update()
        
        # 最终更新
        if self.agent.buffer.size > 0:
            self.agent.update()
        
        # 计算指标
        success_rate = 1.0 if final_return >= ENV_CONFIG['success_threshold'] else 0.0
        training_time = time.time() - episode_start_time
        
        return {
            'episode_reward': episode_reward,
            'episode_length': episode_length,
            'avg_holding_days': holding_days,
            'success_rate': success_rate,
            'final_return': final_return,
            'training_time': training_time
        }
    
    def evaluate(self, n_episodes: int = 10) -> Dict:
        """评估模型性能"""
        total_reward = 0
        total_holding_days = 0
        successful_trades = 0
        
        for _ in range(n_episodes):
            state = self.env.reset()
            episode_reward = 0
            
            done = False
            while not done:
                # 确定性动作选择
                action, _, _, _ = self.agent.get_action(state, deterministic=True)
                next_state, reward, done, _ = self.env.step(action)
                
                episode_reward += reward
                state = next_state
            
            total_reward += episode_reward
            total_holding_days += self.env.holding_days
            
            # 检查是否成功
            if hasattr(self.env, 'position_history') and self.env.position_history:
                final_return = self.env.position_history[-1]['return']
                if final_return >= ENV_CONFIG['success_threshold']:
                    successful_trades += 1
        
        return {
            'avg_reward': total_reward / n_episodes,
            'avg_holding_days': total_holding_days / n_episodes,
            'success_rate': successful_trades / n_episodes
        }
    
    def train(self) -> bool:
        """执行训练"""
        self.logger.logger.info("开始Sell Knowledge RL训练")
        
        for episode in tqdm(range(self.max_episodes), desc="Sell Knowledge Training"):
            # 训练一个episode
            metrics = self.train_episode()
            
            # 记录指标
            self.logger.log_episode('sell_knowledge', episode, metrics)
            
            # 更新智能体统计
            self.agent.episode_count += 1
            self.agent.total_rewards.append(metrics['episode_reward'])
            
            # 早停检查
            if metrics['episode_reward'] > self.best_reward + self.early_stopping_threshold:
                self.best_reward = metrics['episode_reward']
                self.patience_counter = 0
                
                # 保存最佳模型
                best_model_path = os.path.join(self.model_save_path, 'best_model.pth')
                self.agent.save_model(best_model_path)
            else:
                self.patience_counter += 1
            
            # 定期保存
            if (episode + 1) % self.save_freq == 0:
                model_path = os.path.join(self.model_save_path, f'model_episode_{episode + 1}.pth')
                self.agent.save_model(model_path)
            
            # 定期评估
            if (episode + 1) % self.eval_freq == 0:
                eval_metrics = self.evaluate()
                self.logger.logger.info(f"评估结果 - Episode {episode + 1}: {eval_metrics}")
            
            # 早停
            if self.patience_counter >= self.early_stopping_patience:
                self.logger.logger.info(f"早停触发 - Episode {episode + 1}")
                break
        
        # 保存最终模型
        final_model_path = os.path.join(self.model_save_path, 'final_model.pth')
        self.agent.save_model(final_model_path)
        
        self.logger.logger.info("Sell Knowledge RL训练完成")
        return True

class ProTraderRLTrainer:
    """Pro Trader RL主训练器"""
    
    def __init__(self):
        # 创建实验目录
        self.experiment_name = EXPERIMENT_CONFIG['experiment_name']
        self.log_dir = os.path.join(LOGS_DIR, self.experiment_name)
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 创建日志记录器
        self.logger = TrainingLogger(self.log_dir, self.experiment_name)
        
        # 设置随机种子（在logger初始化之后）
        if EXPERIMENT_CONFIG['reproducible']:
            self.set_random_seed(EXPERIMENT_CONFIG['random_seed'])
        
        # 数据预处理器
        self.preprocessor = DataPreprocessor()
        
        self.logger.logger.info(f"Pro Trader RL训练器初始化完成 - 实验: {self.experiment_name}")
    
    def set_random_seed(self, seed: int):
        """设置随机种子"""
        np.random.seed(seed)
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
            torch.cuda.manual_seed_all(seed)
        
        self.logger.logger.info(f"随机种子设置为: {seed}")
    
    def prepare_data(self):
        """准备训练数据"""
        self.logger.logger.info("开始准备训练数据...")
        
        # 加载原始数据
        data_dict = self.preprocessor.load_data()
        
        # 准备训练数据
        train_data = self.preprocessor.prepare_training_data(
            data_dict, TRAIN_START_DATE, TRAIN_END_DATE, mode='train'
        )
        
        if train_data['processed_data'].empty:
            raise ValueError("训练数据为空，请检查数据文件和配置")
        
        # 创建数据生成器
        self.data_generator = TradingDataGenerator(train_data['processed_data'])
        
        self.logger.logger.info(f"训练数据准备完成，共 {len(train_data['processed_data'])} 条记录")
        
        return train_data
    
    def train_buy_knowledge(self) -> bool:
        """训练Buy Knowledge RL"""
        self.logger.logger.info("="*50)
        self.logger.logger.info("开始训练Buy Knowledge RL")
        self.logger.logger.info("="*50)
        
        # 创建训练器
        trainer = BuyKnowledgeTrainer(self.data_generator, self.logger)
        
        # 执行训练
        success = trainer.train()
        
        # 记录训练统计
        stats = trainer.agent.get_training_stats()
        self.logger.logger.info(f"Buy Knowledge RL训练统计: {stats}")
        
        return success
    
    def train_sell_knowledge(self) -> bool:
        """训练Sell Knowledge RL"""
        self.logger.logger.info("="*50)
        self.logger.logger.info("开始训练Sell Knowledge RL")
        self.logger.logger.info("="*50)
        
        # 重置数据生成器
        self.data_generator.reset()
        
        # 创建训练器
        trainer = SellKnowledgeTrainer(self.data_generator, self.logger)
        
        # 执行训练
        success = trainer.train()
        
        # 记录训练统计
        stats = trainer.agent.get_training_stats()
        self.logger.logger.info(f"Sell Knowledge RL训练统计: {stats}")
        
        return success
    
    def train(self):
        """执行完整训练流程"""
        start_time = time.time()
        
        try:
            # 准备数据
            self.prepare_data()
            
            # 训练Buy Knowledge RL
            buy_success = self.train_buy_knowledge()
            if not buy_success:
                self.logger.logger.error("Buy Knowledge RL训练失败")
                return False
            
            # 训练Sell Knowledge RL
            sell_success = self.train_sell_knowledge()
            if not sell_success:
                self.logger.logger.error("Sell Knowledge RL训练失败")
                return False
            
            # 保存训练指标
            self.logger.save_metrics()
            
            # 绘制训练曲线
            self.logger.plot_training_curves()
            
            # 计算总训练时间
            total_time = time.time() - start_time
            self.logger.logger.info(f"训练完成！总耗时: {total_time:.2f}秒")
            
            return True
            
        except Exception as e:
            self.logger.logger.error(f"训练过程中发生错误: {e}")
            import traceback
            self.logger.logger.error(traceback.format_exc())
            return False

def main():
    """主训练函数"""
    print("🚀 开始Pro Trader RL训练")
    print("="*60)
    
    # 创建训练器
    trainer = ProTraderRLTrainer()
    
    # 执行训练
    success = trainer.train()
    
    if success:
        print("\n✅ 训练成功完成！")
        print(f"📊 训练日志保存在: {trainer.log_dir}")
        print(f"🤖 模型保存在: {MODEL_SAVE_DIR}")
    else:
        print("\n❌ 训练失败，请检查日志")
    
    print("="*60)

if __name__ == "__main__":
    main()
