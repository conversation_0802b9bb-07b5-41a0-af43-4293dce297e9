# Pro Trader RL: 强化学习交易系统

基于论文《Pro Trader RL: Reinforcement learning framework for generating trading knowledge by mimicking the decision-making patterns of professional traders》的完整实现。

## 📋 项目概述

Pro Trader RL是一个专业的强化学习交易系统，通过模拟专业交易员的决策模式来生成交易知识。系统包含两个核心RL智能体：

- **Buy Knowledge RL**: 学习何时买入股票（预测收益率≥10%的概率）
- **Sell Knowledge RL**: 学习何时卖出股票（优化持有期和卖出时机）

## 🏗️ 系统架构

```
Pro Trader RL/
├── config.py                    # 配置文件（神经网络、因子选择、训练参数等）
├── main.py                      # 主程序入口
├── data_preprocessing.py        # 数据预处理模块
├── calculate_stock_factors_test.py  # 因子计算（69个高级因子）
├── download_cyb_new.py         # 数据下载脚本
├── rl_environments.py          # 强化学习环境
├── ppo_agent.py                # PPO智能体实现
├── trainer.py                  # 训练模块
├── trading_strategy.py         # 交易策略和回测
├── tushare_data_cyb/          # 数据目录
├── models/                     # 模型保存目录
├── logs/                       # 日志目录
└── results/                    # 回测结果目录
```

## 🔧 环境配置

### 1. Python环境
推荐使用Python 3.8+，系统默认使用：
```
D:\ProgramData\miniconda3\envs\v8new\python.exe
```

### 2. 依赖包安装
```bash
pip install pandas numpy torch gym matplotlib tqdm scikit-learn
pip install talib scipy joblib tushare
```

### 3. 数据准备
确保以下数据文件存在于 `tushare_data_cyb/` 目录：
- `stock_basic_cyb.csv` - 股票基本信息
- `stock_daily_cyb.csv` - 股票日线数据
- `daily_basic_cyb.csv` - 每日基本面数据
- `index_daily_cyb.csv` - 指数日线数据

## 🚀 快速开始

### 完整流程（推荐）
```bash
python main.py --mode full
```

### 分步执行
```bash
# 1. 下载数据
python main.py --mode download

# 2. 计算因子
python main.py --mode factors

# 3. 训练模型
python main.py --mode train

# 4. 策略回测
python main.py --mode backtest

# 5. 测试模块
python main.py --mode test
```

### 跳过某些步骤
```bash
# 跳过数据下载和因子计算，直接训练
python main.py --skip-download --skip-factors

# 只进行回测
python main.py --skip-download --skip-factors --skip-train
```

## ⚙️ 配置说明

### 核心配置项（config.py）

#### 1. 数据配置
```python
# 时间范围
TRAIN_START_DATE = '20200101'  # 训练开始日期
TRAIN_END_DATE = '20230101'    # 训练结束日期
TEST_START_DATE = '20230101'   # 测试开始日期
TEST_END_DATE = '20241231'     # 测试结束日期
```

#### 2. 因子选择
系统包含69个高级技术因子，分为5大类：
- **量价动量因子组**: 4个因子 × 4个时间窗口
- **波动率不对称因子组**: 4个因子 × 4个时间窗口  
- **冲击强度因子组**: 4个因子 × 4个时间窗口
- **高阶矩与分布形态因子**: 13个因子
- **流动性与交易行为因子**: 16个因子
- **价格动态与微观结构因子**: 12个因子
- **创新与组合因子**: 32个因子

#### 3. 神经网络架构
```python
# Buy Knowledge RL网络
BUY_KNOWLEDGE_CONFIG = {
    'actor_layers': [因子数量, 128, 64, 32, 2],
    'critic_layers': [因子数量, 128, 64, 32, 1],
    'actor_lr': 3e-4,
    'critic_lr': 3e-4,
}

# Sell Knowledge RL网络  
SELL_KNOWLEDGE_CONFIG = {
    'actor_layers': [因子数量+1, 128, 64, 32, 2],
    'critic_layers': [因子数量+1, 128, 64, 32, 1],
    'actor_lr': 3e-4,
    'critic_lr': 3e-4,
}
```

#### 4. PPO算法参数
```python
PPO_CONFIG = {
    'learning_rate': 3e-4,
    'gamma': 0.99,           # 折扣因子
    'gae_lambda': 0.95,      # GAE参数
    'clip_range': 0.2,       # PPO裁剪参数
    'n_steps': 2048,         # 每次更新步数
    'batch_size': 64,        # 批大小
    'n_epochs': 10,          # 每次更新轮数
}
```

#### 5. 交易环境配置
```python
ENV_CONFIG = {
    'initial_balance': 10000.0,    # 初始资金
    'max_positions': 10,           # 最大持仓数
    'max_position_pct': 0.1,       # 单只股票最大仓位
    'success_threshold': 0.1,      # 成功阈值10%
}
```

## 📊 因子体系

### 论文中的69个变量分类

#### 1. 量价动量因子组 (16个)
- `VOLUME_PRICE_MOMENTUM_{5,10,20,30}`: 上涨日量价动量-下跌日量价惩罚
- `VOLATILITY_ASYMMETRY_{5,10,20,30}`: 波动率不对称因子
- `SHOCK_INTENSITY_{5,10,20,30}`: 冲击强度因子  
- `MARKET_DOWNSIDE_EXCESS_30`: 市场下行日超额收益因子

#### 2. 高阶矩与分布形态因子 (13个)
- `SKEWNESS_{5,10,20,30}`: 滚动收益率偏度
- `KURTOSIS_{5,10,20,30}`: 滚动收益率峰度
- `DOWNSIDE_DEV_{5,10,20,30}`: 下行标准差
- `CVAR_5PCT_60`: 5%历史条件风险价值

#### 3. 流动性与交易行为因子 (16个)
- `AMIHUD_ILLIQ_{5,10,20,30}`: Amihud非流动性指标
- `TURNOVER_VOL_{5,10,20,30}`: 换手率波动率
- `VOL_CONCENTRATION_HHI_{5,10,20,30}`: 成交量集中度
- `RET_TURN_CORR_{5,10,20,30}`: 收益率与换手率相关性

#### 4. 价格动态与微观结构因子 (12个)
- `RET_AUTOCORR_LAG1_{5,10,20,30}`: 收益率自相关性
- `CLOSE_TO_HILO_RATIO_{5,10,20,30}`: 收盘价在日内高低价中的位置
- `MAX_DRAWDOWN_{5,10,20,30}`: 最大回撤

#### 5. 创新与组合因子 (32个)
- `VOL_OF_VOL_10_{5,10,20,30}`: 波动率的波动率
- `PRICE_VS_MEDIAN_{5,10,20,30}`: 股价相对滚动中位数位置
- `UP_DOWN_VOL_RATIO_{5,10,20,30}`: 上涨日/下跌日成交量比率
- `PE_ADJ_MOMENTUM_{5,10,20,30}`: PE调整后的动量
- `ZERO_TRADE_RATIO_{5,10,20,30}`: 零成交日占比
- `MARKET_DEPTH_PROXY_{5,10,20,30}`: 市场深度代理指标
- `GAP_RATIO_{5,10,20,30}`: 收益缺口率
- `REAL_VOL_VS_RANGE_{5,10,20,30}`: 已实现波动率与日内振幅比
- `TAIL_RATIO_60`: 尾部比率

## 🎯 训练流程

### 1. Buy Knowledge RL训练
- **目标**: 预测股票未来收益率≥10%的概率
- **输入**: 69维标准化因子向量
- **输出**: [<10%概率, ≥10%概率]
- **奖励函数**: 
  - 正确预测: +1分
  - 错误预测: 0分

### 2. Sell Knowledge RL训练  
- **目标**: 优化持有期和卖出时机
- **输入**: 69维因子向量 + 当前收益率
- **输出**: [持有概率, 卖出概率]
- **奖励函数**:
  - 收益率≥10%: 1.0~2.0分（基于相对排名）
  - 收益率<10%: -1.0分

### 3. 止损规则
- **跌幅止损**: 收益率≤-10%
- **横盘止损**: 20天内收益率<10%
- **动态止损**: 跟踪止损5% + 波动率倍数

## 📈 回测系统

### 投资组合管理
- **初始资金**: 10,000元
- **最大持仓**: 10只股票
- **单只股票最大仓位**: 10%
- **交易费用**: 0.1%

### 交易逻辑
1. **买入信号**: Buy Knowledge RL预测概率>50%的股票
2. **卖出信号**: 
   - Sell Knowledge RL决策卖出
   - 触发止损规则
   - 达到最大持有期（120天）

### 绩效指标
- 总收益率、年化收益率
- 夏普比率、最大回撤
- 胜率、平均持有天数
- 交易次数、盈亏比

## 📁 输出文件

### 训练输出
- `models/buy_knowledge/best_model.pth` - Buy Knowledge最佳模型
- `models/sell_knowledge/best_model.pth` - Sell Knowledge最佳模型
- `logs/` - 训练日志和TensorBoard文件

### 回测输出
- `results/backtest_YYYYMMDD_HHMMSS/`
  - `trade_history.csv` - 详细交易记录
  - `daily_values.csv` - 每日净值曲线
  - `performance_metrics.json` - 绩效指标

## 🔍 使用示例

### 1. 自定义因子选择
```python
# 在config.py中修改
SELECTED_FACTORS = [
    'VOLUME_PRICE_MOMENTUM_20',
    'VOLATILITY_ASYMMETRY_10', 
    'SHOCK_INTENSITY_30',
    # ... 添加你需要的因子
]
```

### 2. 调整网络架构
```python
# 修改网络层数和神经元数量
BUY_KNOWLEDGE_CONFIG = {
    'actor_layers': [因子数量, 256, 128, 64, 2],  # 更深的网络
    'critic_layers': [因子数量, 256, 128, 64, 1],
}
```

### 3. 修改训练参数
```python
TRAINING_CONFIG = {
    'max_episodes': 5000,        # 减少训练轮数
    'early_stopping_patience': 20,  # 早停耐心
}
```

## ⚠️ 注意事项

1. **数据质量**: 确保股票数据完整且无异常值
2. **计算资源**: 训练过程需要较多计算资源，建议使用GPU
3. **内存使用**: 因子计算可能占用大量内存，注意监控
4. **过拟合**: 注意验证集表现，避免过拟合
5. **实盘风险**: 回测结果不代表实盘表现，请谨慎使用

## 🐛 常见问题

### Q1: 数据文件不存在
**A**: 确保运行了数据下载脚本，或手动准备数据文件

### Q2: 训练过程中内存不足
**A**: 减少批大小或使用更少的并行进程

### Q3: 模型训练不收敛
**A**: 调整学习率、网络架构或检查数据质量

### Q4: 回测结果异常
**A**: 检查交易逻辑、费用设置和数据完整性

## 📚 参考文献

```bibtex
@article{pro_trader_rl_2024,
  title={Pro Trader RL: Reinforcement learning framework for generating trading knowledge by mimicking the decision-making patterns of professional traders},
  author={Authors},
  journal={Journal},
  year={2024}
}
```

## 📄 许可证

本项目仅供学术研究使用，不构成投资建议。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

---

**免责声明**: 本系统仅供研究和学习使用，不构成任何投资建议。投资有风险，入市需谨慎。
