2025-08-10 21:31:46,788 - __main__ - INFO - 日志系统初始化完成
2025-08-10 21:31:46,789 - __main__ - INFO - 日志文件: logs\main_20250810_213146.log
2025-08-10 21:31:46,789 - __main__ - INFO - 使用Python环境: D:\ProgramData\miniconda3\envs\v8new\python.exe
2025-08-10 21:31:46,796 - __main__ - INFO - 创建目录: models
2025-08-10 21:31:46,834 - __main__ - INFO - 创建目录: results
2025-08-10 21:31:47,097 - __main__ - INFO - GPU可用: NVIDIA GeForce GTX 1650
2025-08-10 21:31:47,097 - __main__ - INFO - ============================================================
2025-08-10 21:31:47,097 - __main__ - INFO - 开始训练Pro Trader RL模型
2025-08-10 21:31:47,097 - __main__ - INFO - ============================================================
2025-08-10 21:31:47,210 - __main__ - ERROR - 模型训练过程中发生错误: 'ProTraderRLTrainer' object has no attribute 'logger'
2025-08-10 21:31:47,211 - __main__ - ERROR - Traceback (most recent call last):
  File "D:\alpha\main.py", line 173, in train_models
    trainer = ProTraderRLTrainer()
  File "D:\alpha\trainer.py", line 496, in __init__
    self.set_random_seed(EXPERIMENT_CONFIG['random_seed'])
  File "D:\alpha\trainer.py", line 519, in set_random_seed
    self.logger.logger.info(f"随机种子设置为: {seed}")
AttributeError: 'ProTraderRLTrainer' object has no attribute 'logger'

