# -*- coding: utf-8 -*-
"""
Data Preprocessing Module for Pro Trader RL
数据预处理模块：包含数据集生成、变量生成、数据标准化等功能
"""

import pandas as pd
import numpy as np
import talib
from typing import Dict, List, Tuple, Optional
import warnings
from datetime import datetime, timedelta
import logging
from config import *

warnings.filterwarnings('ignore')

class DataPreprocessor:
    """数据预处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.donchian_config = DONCHIAN_CONFIG
        self.normalization_methods = NORMALIZATION_METHODS
        
    def load_data(self) -> Dict[str, pd.DataFrame]:
        """
        加载所有数据文件
        Returns:
            Dict包含所有数据DataFrame
        """
        data = {}
        
        try:
            # 加载股票基本信息
            stock_basic_path = os.path.join(DATA_DIR, DATA_FILES['stock_basic'])
            data['stock_basic'] = pd.read_csv(stock_basic_path)
            self.logger.info(f"加载股票基本信息: {len(data['stock_basic'])} 条")
            
            # 加载股票日线数据
            stock_daily_path = os.path.join(DATA_DIR, DATA_FILES['stock_daily'])
            data['stock_daily'] = pd.read_csv(stock_daily_path)
            data['stock_daily']['trade_date'] = pd.to_datetime(data['stock_daily']['trade_date'])
            self.logger.info(f"加载股票日线数据: {len(data['stock_daily'])} 条")
            
            # 加载每日基本面数据
            daily_basic_path = os.path.join(DATA_DIR, DATA_FILES['daily_basic'])
            if os.path.exists(daily_basic_path):
                data['daily_basic'] = pd.read_csv(daily_basic_path)
                data['daily_basic']['trade_date'] = pd.to_datetime(data['daily_basic']['trade_date'])
                self.logger.info(f"加载每日基本面数据: {len(data['daily_basic'])} 条")
            
            # 加载指数数据
            index_daily_path = os.path.join(DATA_DIR, DATA_FILES['index_daily'])
            if os.path.exists(index_daily_path):
                data['index_daily'] = pd.read_csv(index_daily_path)
                data['index_daily']['trade_date'] = pd.to_datetime(data['index_daily']['trade_date'])
                self.logger.info(f"加载指数数据: {len(data['index_daily'])} 条")
            
            # 加载因子数据
            factors_path = os.path.join(DATA_DIR, DATA_FILES['stock_factors'])
            if os.path.exists(factors_path):
                data['stock_factors'] = pd.read_csv(factors_path)
                data['stock_factors']['trade_date'] = pd.to_datetime(data['stock_factors']['trade_date'])
                self.logger.info(f"加载因子数据: {len(data['stock_factors'])} 条")
                
        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
            raise
            
        return data
    
    def generate_donchian_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        生成Donchian Channel交易信号
        Args:
            df: 股票价格数据
        Returns:
            包含信号的DataFrame
        """
        df = df.copy()
        
        # 计算Donchian Channel
        upper_window = self.donchian_config['upper_window']
        lower_window = self.donchian_config['lower_window']
        
        df['donchian_upper'] = df['high'].rolling(window=upper_window).max()
        df['donchian_lower'] = df['low'].rolling(window=lower_window).min()
        
        # 生成买入信号
        df['buy_signal'] = 0
        df['sell_signal'] = 0
        
        # 买入信号：当前最高价突破上轨且前一日没有买入信号
        buy_condition = (df['high'] > df['donchian_upper'].shift(1)) & (df['buy_signal'].shift(1) != 1)
        df.loc[buy_condition, 'buy_signal'] = 1
        
        # 卖出信号：最低价跌破下轨且之前有买入信号
        sell_condition = (df['low'] < df['donchian_lower'].shift(1))
        
        # 标记每个买入信号对应的卖出信号
        for idx in df.index[df['buy_signal'] == 1]:
            # 找到该买入信号之后的第一个卖出点
            future_sells = df.loc[df.index > idx]
            sell_idx = future_sells[sell_condition.loc[future_sells.index]].index
            if len(sell_idx) > 0:
                df.loc[sell_idx[0], 'sell_signal'] = 1
        
        # 计算信号收益率
        df['signal_return'] = np.nan
        buy_indices = df.index[df['buy_signal'] == 1]
        
        for buy_idx in buy_indices:
            # 找到对应的卖出信号
            future_data = df.loc[df.index > buy_idx]
            sell_indices = future_data.index[future_data['sell_signal'] == 1]
            
            if len(sell_indices) > 0:
                sell_idx = sell_indices[0]
                # 计算收益率：第二天开盘买入，卖出信号当天开盘卖出
                buy_price = df.loc[buy_idx + 1, 'open'] if buy_idx + 1 < len(df) else df.loc[buy_idx, 'close']
                sell_price = df.loc[sell_idx + 1, 'open'] if sell_idx + 1 < len(df) else df.loc[sell_idx, 'close']
                
                signal_return = (sell_price - buy_price) / buy_price
                df.loc[buy_idx, 'signal_return'] = signal_return
        
        return df
    
    def calculate_additional_factors(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算论文中提到的技术指标（补充现有因子）
        """
        df = df.copy()
        
        # 计算ATR (Average True Range)
        df['atr'] = talib.ATR(df['high'].values, df['low'].values, df['close'].values, timeperiod=10)
        
        # 计算Super Trend
        def calculate_supertrend(high, low, close, period=14, multiplier=2):
            """计算Super Trend指标"""
            atr = talib.ATR(high.values, low.values, close.values, timeperiod=period)
            hl_avg = (high + low) / 2
            
            upper_band = hl_avg + multiplier * atr
            lower_band = hl_avg - multiplier * atr
            
            supertrend = pd.Series(index=close.index)
            trend = pd.Series(index=close.index)
            
            for i in range(1, len(close)):
                if close.iloc[i] > upper_band.iloc[i-1]:
                    supertrend.iloc[i] = lower_band.iloc[i]
                    trend.iloc[i] = 1
                elif close.iloc[i] < lower_band.iloc[i-1]:
                    supertrend.iloc[i] = upper_band.iloc[i]
                    trend.iloc[i] = -1
                else:
                    supertrend.iloc[i] = supertrend.iloc[i-1]
                    trend.iloc[i] = trend.iloc[i-1]
            
            return supertrend, trend
        
        # 计算Super Trend (14, 2)
        df['supertrend_14'], df['supertrend_trend_14'] = calculate_supertrend(
            df['high'], df['low'], df['close'], 14, 2)
        
        # 计算Super Trend (21, 1)
        df['supertrend_21'], df['supertrend_trend_21'] = calculate_supertrend(
            df['high'], df['low'], df['close'], 21, 1)
        
        # 计算MFI (Money Flow Index)
        df['mfi'] = talib.MFI(df['high'].values, df['low'].values, 
                             df['close'].values, df['vol'].values, timeperiod=14)
        
        # 计算RSI
        df['rsi'] = talib.RSI(df['close'].values, timeperiod=14)
        
        # 计算Stock(N) - ATR相对变化
        for n in range(1, 13):  # 1-12个月
            df[f'stock_{n}'] = df['atr'] / df['atr'].shift(n*20)  # 假设每月20交易日
        
        # 计算AVG Stock
        stock_cols = [f'stock_{n}' for n in range(1, 13)]
        df['avg_stock'] = df[stock_cols].mean(axis=1)
        
        return df
    
    def normalize_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        数据标准化 - 根据论文中的标准化方法
        """
        df = df.copy()
        
        # 基于Donchian通道的价格标准化
        for col in self.normalization_methods.get('price_based', []):
            if col in df.columns and 'donchian_upper' in df.columns:
                if col in ['close', 'low', 'high']:
                    df[f'{col}_normalized'] = df[col] / df['donchian_upper']
                else:
                    df[f'{col}_normalized'] = df[col] / df['high']
        
        # Donchian通道本身的标准化
        if 'donchian_upper' in df.columns and 'high' in df.columns:
            df['donchian_upper_normalized'] = df['donchian_upper'] / df['high']
        if 'donchian_lower' in df.columns and 'low' in df.columns:
            df['donchian_lower_normalized'] = df['donchian_lower'] / df['low']
        
        # 差分标准化（当日vs前日）
        for col in self.normalization_methods.get('diff_based', []):
            if col in df.columns:
                df[f'{col}_normalized'] = df[col] / (df[col].shift(1) + 1e-8)
        
        # 百分比标准化
        for col in self.normalization_methods.get('percentage_based', []):
            if col in df.columns:
                df[f'{col}_normalized'] = df[col] * 0.01
        
        # 比率标准化
        for col in self.normalization_methods.get('ratio_based', []):
            if col in df.columns:
                df[f'{col}_normalized'] = df[col] * 0.01
        
        # Z分数标准化（技术因子）
        for col in self.normalization_methods.get('zscore_based', []):
            if col in df.columns:
                mean_val = df[col].rolling(window=60).mean()
                std_val = df[col].rolling(window=60).std()
                df[f'{col}_normalized'] = (df[col] - mean_val) / (std_val + 1e-8)
        
        # 最小最大值标准化（Stock相关指标）
        stock_cols = [col for col in df.columns if col.startswith('stock_')]
        if stock_cols:
            # 计算所有Stock指标的最小最大值
            all_stock_data = df[stock_cols].values.flatten()
            min_val = np.nanmin(all_stock_data)
            max_val = np.nanmax(all_stock_data)
            
            for col in stock_cols:
                df[f'{col}_normalized'] = (df[col] - min_val) / (max_val - min_val + 1e-8)
        
        return df
    
    def prepare_training_data(self, data_dict: Dict[str, pd.DataFrame], 
                            start_date: str, end_date: str, 
                            mode: str = 'train') -> Dict[str, pd.DataFrame]:
        """
        准备训练/测试数据
        Args:
            data_dict: 原始数据字典
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            mode: 模式 ('train' 或 'test')
        Returns:
            处理后的数据字典
        """
        self.logger.info(f"准备{mode}数据，时间范围: {start_date} - {end_date}")
        
        start_date = pd.to_datetime(start_date, format='%Y%m%d')
        end_date = pd.to_datetime(end_date, format='%Y%m%d')
        
        prepared_data = {}
        
        # 处理股票因子数据
        if 'stock_factors' in data_dict:
            factors_data = data_dict['stock_factors']
            
            # 时间筛选
            mask = (factors_data['trade_date'] >= start_date) & (factors_data['trade_date'] <= end_date)
            factors_filtered = factors_data[mask].copy()
            
            # 按股票代码分组处理
            processed_stocks = []
            
            for ts_code, group in factors_filtered.groupby('ts_code'):
                if len(group) < ENV_CONFIG['min_history']:
                    continue
                
                group = group.sort_values('trade_date').reset_index(drop=True)
                
                # 生成Donchian信号
                group = self.generate_donchian_signals(group)
                
                # 计算额外因子
                group = self.calculate_additional_factors(group)
                
                # 数据标准化
                group = self.normalize_data(group)
                
                # 只保留有买入信号的数据（用于训练）
                if mode == 'train':
                    group = group[group['buy_signal'] == 1].copy()
                
                if not group.empty:
                    processed_stocks.append(group)
            
            if processed_stocks:
                prepared_data['processed_data'] = pd.concat(processed_stocks, ignore_index=True)
                self.logger.info(f"处理完成，共有 {len(prepared_data['processed_data'])} 条记录")
            else:
                self.logger.warning("没有处理出有效数据")
                prepared_data['processed_data'] = pd.DataFrame()
        
        return prepared_data
    
    def create_features_labels(self, df: pd.DataFrame, 
                             feature_cols: List[str] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        创建特征和标签
        Args:
            df: 处理后的数据
            feature_cols: 特征列名列表
        Returns:
            (features, labels) tuple
        """
        if feature_cols is None:
            # 使用配置中定义的因子
            feature_cols = []
            
            # 添加标准化后的基础因子
            for col in BASIC_FACTORS:
                normalized_col = f'{col}_normalized'
                if normalized_col in df.columns:
                    feature_cols.append(normalized_col)
                elif col in df.columns:
                    feature_cols.append(col)
            
            # 添加技术因子
            for col in TECHNICAL_FACTORS:
                if col in df.columns:
                    feature_cols.append(col)
            
            # 添加基本面因子
            for col in FUNDAMENTAL_FACTORS:
                normalized_col = f'{col}_normalized'
                if normalized_col in df.columns:
                    feature_cols.append(normalized_col)
                elif col in df.columns:
                    feature_cols.append(col)
        
        # 提取特征
        available_cols = [col for col in feature_cols if col in df.columns]
        features = df[available_cols].fillna(0).values
        
        # 创建标签（基于signal_return）
        labels = np.zeros(len(df))
        if 'signal_return' in df.columns:
            # 标签：1表示收益>=10%，0表示收益<10%
            labels = (df['signal_return'].fillna(0) >= 0.1).astype(int).values
        
        self.logger.info(f"创建特征: {features.shape}, 标签分布: {np.bincount(labels)}")
        
        return features, labels
    
    def split_data(self, features: np.ndarray, labels: np.ndarray, 
                   validation_split: float = 0.2) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        划分训练集和验证集
        """
        n_samples = len(features)
        n_val = int(n_samples * validation_split)
        
        # 随机打乱
        indices = np.random.permutation(n_samples)
        
        train_indices = indices[:-n_val] if n_val > 0 else indices
        val_indices = indices[-n_val:] if n_val > 0 else []
        
        X_train = features[train_indices]
        y_train = labels[train_indices]
        
        if len(val_indices) > 0:
            X_val = features[val_indices]
            y_val = labels[val_indices]
        else:
            X_val = np.array([])
            y_val = np.array([])
        
        return X_train, X_val, y_train, y_val

class TradingDataGenerator:
    """交易数据生成器 - 为RL环境提供数据"""
    
    def __init__(self, processed_data: pd.DataFrame):
        self.data = processed_data.copy()
        self.current_idx = 0
        self.logger = logging.getLogger(__name__)
        
    def reset(self):
        """重置数据生成器"""
        self.current_idx = 0
        
    def get_batch(self, batch_size: int = 1) -> Dict:
        """
        获取一批数据用于RL训练
        """
        if self.current_idx >= len(self.data):
            return None
        
        end_idx = min(self.current_idx + batch_size, len(self.data))
        batch_data = self.data.iloc[self.current_idx:end_idx].copy()
        
        self.current_idx = end_idx
        
        # 提取特征
        feature_cols = SELECTED_FACTORS
        available_cols = [col for col in feature_cols if col in batch_data.columns]
        
        # 如果某些因子不存在，用0填充
        features = np.zeros((len(batch_data), len(feature_cols)))
        for i, col in enumerate(feature_cols):
            if col in available_cols:
                features[:, i] = batch_data[col].fillna(0).values
        
        # 构建返回数据
        batch = {
            'features': features,
            'ts_codes': batch_data['ts_code'].values,
            'trade_dates': batch_data['trade_date'].values,
            'returns': batch_data.get('signal_return', pd.Series(0, index=batch_data.index)).fillna(0).values,
            'buy_signals': batch_data.get('buy_signal', pd.Series(0, index=batch_data.index)).values,
            'prices': {
                'open': batch_data['open'].values,
                'high': batch_data['high'].values,
                'low': batch_data['low'].values,
                'close': batch_data['close'].values
            }
        }
        
        return batch
    
    def get_stock_history(self, ts_code: str, current_date: pd.Timestamp, 
                         window: int = 120) -> Optional[pd.DataFrame]:
        """
        获取指定股票的历史数据
        """
        stock_data = self.data[self.data['ts_code'] == ts_code].copy()
        stock_data = stock_data[stock_data['trade_date'] <= current_date]
        
        if len(stock_data) >= window:
            return stock_data.tail(window)
        elif len(stock_data) > 0:
            return stock_data
        else:
            return None

def main():
    """测试数据预处理模块"""
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建数据预处理器
    preprocessor = DataPreprocessor()
    
    # 加载数据
    print("加载原始数据...")
    data_dict = preprocessor.load_data()
    
    # 准备训练数据
    print("准备训练数据...")
    train_data = preprocessor.prepare_training_data(
        data_dict, TRAIN_START_DATE, TRAIN_END_DATE, mode='train'
    )
    
    if 'processed_data' in train_data and not train_data['processed_data'].empty:
        print(f"训练数据准备完成，共 {len(train_data['processed_data'])} 条记录")
        
        # 创建特征和标签
        features, labels = preprocessor.create_features_labels(train_data['processed_data'])
        print(f"特征维度: {features.shape}")
        print(f"标签分布: {np.bincount(labels)}")
        
        # 创建数据生成器
        data_generator = TradingDataGenerator(train_data['processed_data'])
        
        # 测试批次数据获取
        batch = data_generator.get_batch(batch_size=5)
        if batch:
            print(f"批次数据特征形状: {batch['features'].shape}")
            print(f"批次包含股票: {batch['ts_codes']}")
        
    else:
        print("⚠️  训练数据为空，请检查数据文件和日期范围")

if __name__ == "__main__":
    main()
