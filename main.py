# -*- coding: utf-8 -*-
"""
Pro Trader RL: Reinforcement Learning Framework for Trading Knowledge
主程序入口：完整的强化学习交易系统

基于论文：Pro Trader RL: Reinforcement learning framework for generating trading
knowledge by mimicking the decision-making patterns of professional traders

功能模块：
1. 数据下载和因子计算
2. 数据预处理和特征工程
3. Buy Knowledge RL训练
4. Sell Knowledge RL训练
5. 交易策略回测
6. 结果分析和可视化
"""

import os
import sys
import argparse
import logging
from datetime import datetime
import warnings

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import *
from data_preprocessing import DataPreprocessor
from trainer import ProTraderRLTrainer
from trading_strategy import run_backtest

warnings.filterwarnings('ignore')

def setup_logging():
    """设置日志系统"""
    # 创建日志目录
    os.makedirs(LOGS_DIR, exist_ok=True)
    
    # 配置日志
    log_file = os.path.join(LOGS_DIR, f'main_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=getattr(logging, LOGGING_CONFIG['log_level']),
        format=LOGGING_CONFIG['log_format'],
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("日志系统初始化完成")
    logger.info(f"日志文件: {log_file}")
    
    return logger

def check_environment():
    """检查运行环境"""
    logger = logging.getLogger(__name__)
    
    # 检查Python环境
    python_path = PYTHON_ENV_PATH
    if not os.path.exists(python_path):
        logger.warning(f"指定的Python环境不存在: {python_path}")
        logger.info("将使用当前Python环境")
    else:
        logger.info(f"使用Python环境: {python_path}")
    
    # 检查必要的目录
    required_dirs = [DATA_DIR, MODEL_SAVE_DIR, LOGS_DIR, RESULTS_DIR]
    for directory in required_dirs:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            logger.info(f"创建目录: {directory}")
    
    # 检查数据文件
    data_files_exist = True
    for file_key, filename in DATA_FILES.items():
        file_path = os.path.join(DATA_DIR, filename)
        if not os.path.exists(file_path):
            logger.warning(f"数据文件不存在: {file_path}")
            data_files_exist = False
    
    if not data_files_exist:
        logger.warning("部分数据文件缺失，请先运行数据下载和因子计算")
    
    # 检查GPU可用性
    try:
        import torch
        if torch.cuda.is_available():
            logger.info(f"GPU可用: {torch.cuda.get_device_name(0)}")
        else:
            logger.info("使用CPU进行训练")
    except ImportError:
        logger.warning("PyTorch未安装，请检查环境配置")
    
    return data_files_exist

def download_data():
    """下载数据"""
    logger = logging.getLogger(__name__)
    logger.info("="*60)
    logger.info("开始下载数据")
    logger.info("="*60)
    
    try:
        # 检查下载脚本是否存在
        download_script = "download_cyb_new.py"
        if not os.path.exists(download_script):
            logger.error(f"数据下载脚本不存在: {download_script}")
            return False
        
        # 执行下载脚本
        import subprocess
        result = subprocess.run([sys.executable, download_script], 
                              capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            logger.info("数据下载完成")
            logger.info(result.stdout)
            return True
        else:
            logger.error("数据下载失败")
            logger.error(result.stderr)
            return False
            
    except Exception as e:
        logger.error(f"数据下载过程中发生错误: {e}")
        return False

def calculate_factors():
    """计算因子"""
    logger = logging.getLogger(__name__)
    logger.info("="*60)
    logger.info("开始计算因子")
    logger.info("="*60)
    
    try:
        # 检查因子计算脚本是否存在
        factor_script = "calculate_stock_factors_test.py"
        if not os.path.exists(factor_script):
            logger.error(f"因子计算脚本不存在: {factor_script}")
            return False
        
        # 执行因子计算脚本
        import subprocess
        result = subprocess.run([sys.executable, factor_script], 
                              capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            logger.info("因子计算完成")
            logger.info(result.stdout)
            return True
        else:
            logger.error("因子计算失败")
            logger.error(result.stderr)
            return False
            
    except Exception as e:
        logger.error(f"因子计算过程中发生错误: {e}")
        return False

def train_models():
    """训练模型"""
    logger = logging.getLogger(__name__)
    logger.info("="*60)
    logger.info("开始训练Pro Trader RL模型")
    logger.info("="*60)
    
    try:
        # 创建训练器
        trainer = ProTraderRLTrainer()
        
        # 执行训练
        success = trainer.train()
        
        if success:
            logger.info("模型训练完成")
            return True
        else:
            logger.error("模型训练失败")
            return False
            
    except Exception as e:
        logger.error(f"模型训练过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def run_strategy_backtest():
    """运行策略回测"""
    logger = logging.getLogger(__name__)
    logger.info("="*60)
    logger.info("开始策略回测")
    logger.info("="*60)
    
    try:
        # 检查模型文件是否存在
        buy_model_path = os.path.join(MODEL_SAVE_DIR, 'buy_knowledge', 'best_model.pth')
        sell_model_path = os.path.join(MODEL_SAVE_DIR, 'sell_knowledge', 'best_model.pth')
        
        if not os.path.exists(buy_model_path):
            logger.error(f"Buy Knowledge模型不存在: {buy_model_path}")
            return False
        
        if not os.path.exists(sell_model_path):
            logger.error(f"Sell Knowledge模型不存在: {sell_model_path}")
            return False
        
        # 执行回测
        run_backtest()
        logger.info("策略回测完成")
        return True
        
    except Exception as e:
        logger.error(f"策略回测过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_modules():
    """测试各个模块"""
    logger = logging.getLogger(__name__)
    logger.info("="*60)
    logger.info("开始模块测试")
    logger.info("="*60)
    
    test_results = {}
    
    # 测试配置模块
    try:
        validate_config()
        create_directories()
        test_results['config'] = True
        logger.info("✅ 配置模块测试通过")
    except Exception as e:
        test_results['config'] = False
        logger.error(f"❌ 配置模块测试失败: {e}")
    
    # 测试数据预处理模块
    try:
        preprocessor = DataPreprocessor()
        logger.info("✅ 数据预处理模块测试通过")
        test_results['preprocessing'] = True
    except Exception as e:
        test_results['preprocessing'] = False
        logger.error(f"❌ 数据预处理模块测试失败: {e}")
    
    # 测试PPO智能体模块
    try:
        from ppo_agent import create_buy_knowledge_agent, create_sell_knowledge_agent
        buy_agent = create_buy_knowledge_agent()
        sell_agent = create_sell_knowledge_agent()
        logger.info("✅ PPO智能体模块测试通过")
        test_results['ppo_agent'] = True
    except Exception as e:
        test_results['ppo_agent'] = False
        logger.error(f"❌ PPO智能体模块测试失败: {e}")
    
    # 测试RL环境模块
    try:
        from rl_environments import StopLossRule
        stop_loss = StopLossRule()
        logger.info("✅ RL环境模块测试通过")
        test_results['rl_environments'] = True
    except Exception as e:
        test_results['rl_environments'] = False
        logger.error(f"❌ RL环境模块测试失败: {e}")
    
    # 输出测试结果
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    logger.info("="*60)
    logger.info(f"模块测试完成: {passed_tests}/{total_tests} 通过")
    
    for module, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{module}: {status}")
    
    return passed_tests == total_tests

def print_system_info():
    """打印系统信息"""
    print("🤖 Pro Trader RL - 强化学习交易系统")
    print("="*60)
    print("基于论文：Pro Trader RL: Reinforcement learning framework")
    print("for generating trading knowledge by mimicking the")
    print("decision-making patterns of professional traders")
    print("="*60)
    print(f"📅 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python版本: {sys.version}")
    print(f"📁 工作目录: {os.getcwd()}")
    print(f"💾 数据目录: {DATA_DIR}")
    print(f"🤖 模型目录: {MODEL_SAVE_DIR}")
    print(f"📊 结果目录: {RESULTS_DIR}")
    print("="*60)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Pro Trader RL - 强化学习交易系统')
    parser.add_argument('--mode', type=str, default='full',
                       choices=['full', 'download', 'factors', 'train', 'backtest', 'test'],
                       help='运行模式')
    parser.add_argument('--skip-download', action='store_true',
                       help='跳过数据下载')
    parser.add_argument('--skip-factors', action='store_true',
                       help='跳过因子计算')
    parser.add_argument('--skip-train', action='store_true',
                       help='跳过模型训练')
    parser.add_argument('--skip-backtest', action='store_true',
                       help='跳过策略回测')
    
    args = parser.parse_args()
    
    # 打印系统信息
    print_system_info()
    
    # 设置日志
    logger = setup_logging()
    
    # 检查环境
    data_files_exist = check_environment()
    
    try:
        if args.mode == 'test':
            # 测试模式
            success = test_modules()
            if success:
                print("\n✅ 所有模块测试通过")
            else:
                print("\n❌ 部分模块测试失败，请检查日志")
            return
        
        elif args.mode == 'download':
            # 仅下载数据
            success = download_data()
            if success:
                print("\n✅ 数据下载完成")
            else:
                print("\n❌ 数据下载失败")
            return
        
        elif args.mode == 'factors':
            # 仅计算因子
            success = calculate_factors()
            if success:
                print("\n✅ 因子计算完成")
            else:
                print("\n❌ 因子计算失败")
            return
        
        elif args.mode == 'train':
            # 仅训练模型
            success = train_models()
            if success:
                print("\n✅ 模型训练完成")
            else:
                print("\n❌ 模型训练失败")
            return
        
        elif args.mode == 'backtest':
            # 仅回测
            success = run_strategy_backtest()
            if success:
                print("\n✅ 策略回测完成")
            else:
                print("\n❌ 策略回测失败")
            return
        
        elif args.mode == 'full':
            # 完整流程
            logger.info("开始执行完整的Pro Trader RL流程")
            
            # 1. 下载数据
            if not args.skip_download and not data_files_exist:
                logger.info("步骤 1/4: 下载数据")
                if not download_data():
                    logger.error("数据下载失败，终止流程")
                    return
            else:
                logger.info("跳过数据下载")
            
            # 2. 计算因子
            if not args.skip_factors:
                logger.info("步骤 2/4: 计算因子")
                if not calculate_factors():
                    logger.error("因子计算失败，终止流程")
                    return
            else:
                logger.info("跳过因子计算")
            
            # 3. 训练模型
            if not args.skip_train:
                logger.info("步骤 3/4: 训练模型")
                if not train_models():
                    logger.error("模型训练失败，终止流程")
                    return
            else:
                logger.info("跳过模型训练")
            
            # 4. 策略回测
            if not args.skip_backtest:
                logger.info("步骤 4/4: 策略回测")
                if not run_strategy_backtest():
                    logger.error("策略回测失败")
                    return
            else:
                logger.info("跳过策略回测")
            
            print("\n🎉 Pro Trader RL完整流程执行完成！")
            print("="*60)
            print("📊 查看结果:")
            print(f"   - 训练日志: {LOGS_DIR}")
            print(f"   - 训练模型: {MODEL_SAVE_DIR}")
            print(f"   - 回测结果: {RESULTS_DIR}")
            print("="*60)
    
    except KeyboardInterrupt:
        logger.info("用户中断程序执行")
        print("\n⚠️  程序被用户中断")
    
    except Exception as e:
        logger.error(f"程序执行过程中发生未预期的错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        print(f"\n❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
