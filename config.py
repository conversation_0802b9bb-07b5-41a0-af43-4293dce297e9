# -*- coding: utf-8 -*-
"""
Pro Trader RL Configuration File
配置文件：设置神经网络架构、因子选择、训练参数等
"""

import os
import numpy as np
from datetime import datetime, timedelta

# =============================================================================
# 1. 数据路径配置
# =============================================================================
# Python环境路径
PYTHON_ENV_PATH = r"D:\ProgramData\miniconda3\envs\v8new\python.exe"

# 数据目录
DATA_DIR = "tushare_data_cyb"

# 数据文件
DATA_FILES = {
    'stock_basic': 'stock_basic_cyb.csv',
    'stock_daily': 'stock_daily_cyb.csv', 
    'daily_basic': 'daily_basic_cyb.csv',
    'index_daily': 'index_daily_cyb.csv',
    'stock_factors': 'stock_factors_cyb.csv'
}

# 模型保存路径
MODEL_SAVE_DIR = "models"
LOGS_DIR = "logs"
RESULTS_DIR = "results"

# =============================================================================
# 2. 时间配置
# =============================================================================
# 训练数据时间范围
TRAIN_START_DATE = '20200101'
TRAIN_END_DATE = '20221231'

# 测试数据时间范围
TEST_START_DATE = '20230101'
TEST_END_DATE = '20250101'

# 验证数据占比
VALIDATION_SPLIT = 0.2

# =============================================================================
# 3. 因子选择配置
# =============================================================================
# 基础价格因子
BASIC_FACTORS = [
    'open', 'high', 'low', 'close', 'vol', 'amount', 'pct_chg'
]

# 技术因子
TECHNICAL_FACTORS = [
    'VOLUME_PRICE_MOMENTUM_5', 'VOLUME_PRICE_MOMENTUM_10', 'VOLUME_PRICE_MOMENTUM_20', 'VOLUME_PRICE_MOMENTUM_30', 'VOLATILITY_ASYMMETRY_5', 'VOLATILITY_ASYMMETRY_10', 'VOLATILITY_ASYMMETRY_20', 'VOLATILITY_ASYMMETRY_30',
    'MARKET_DOWNSIDE_EXCESS_30', 'SKEWNESS_5', 'KURTOSIS_5', 'DOWNSIDE_DEV_5', 'SKEWNESS_10', 'KURTOSIS_10', 'DOWNSIDE_DEV_10', 'SKEWNESS_20', 'KURTOSIS_20', 'DOWNSIDE_DEV_20',
    'SKEWNESS_30', 'KURTOSIS_30', 'DOWNSIDE_DEV_30', 'CVAR_5PCT_60', 'AMIHUD_ILLIQ_5', 'TURNOVER_VOL_5', 'VOL_CONCENTRATION_HHI_5', 'RET_TURN_CORR_5', 'AMIHUD_ILLIQ_10', 'TURNOVER_VOL_10',
    'VOL_CONCENTRATION_HHI_10', 'RET_TURN_CORR_10', 'AMIHUD_ILLIQ_20', 'TURNOVER_VOL_20', 'VOL_CONCENTRATION_HHI_20', 'RET_TURN_CORR_20', 'AMIHUD_ILLIQ_30', 'TURNOVER_VOL_30', 'VOL_CONCENTRATION_HHI_30', 'RET_TURN_CORR_30',
    'RET_AUTOCORR_LAG1_5', 'CLOSE_TO_HILO_RATIO_5', 'MAX_DRAWDOWN_5', 'RET_AUTOCORR_LAG1_10', 'CLOSE_TO_HILO_RATIO_10', 'MAX_DRAWDOWN_10', 'RET_AUTOCORR_LAG1_20', 'CLOSE_TO_HILO_RATIO_20', 'MAX_DRAWDOWN_20', 'RET_AUTOCORR_LAG1_30',
    'CLOSE_TO_HILO_RATIO_30', 'MAX_DRAWDOWN_30', 'VOL_OF_VOL_10_5', 'PRICE_VS_MEDIAN_5', 'UP_DOWN_VOL_RATIO_5', 'PE_ADJ_MOMENTUM_5', 'MARKET_DEPTH_PROXY_5', 'REAL_VOL_VS_RANGE_5', 'VOL_OF_VOL_10_10', 'PRICE_VS_MEDIAN_10',
    'UP_DOWN_VOL_RATIO_10', 'PE_ADJ_MOMENTUM_10', 'MARKET_DEPTH_PROXY_10', 'REAL_VOL_VS_RANGE_10', 'VOL_OF_VOL_10_20', 'PRICE_VS_MEDIAN_20', 'UP_DOWN_VOL_RATIO_20', 'PE_ADJ_MOMENTUM_20', 'MARKET_DEPTH_PROXY_20', 'REAL_VOL_VS_RANGE_20',
    'VOL_OF_VOL_10_30', 'PRICE_VS_MEDIAN_30', 'UP_DOWN_VOL_RATIO_30', 'PE_ADJ_MOMENTUM_30', 'MARKET_DEPTH_PROXY_30', 'REAL_VOL_VS_RANGE_30', 'TAIL_RATIO_60'
]

# 基本面因子 
FUNDAMENTAL_FACTORS = [
    'pe', 'pb', 'turnover_rate', 'volume_ratio'
]

# 所有选用的因子
SELECTED_FACTORS = BASIC_FACTORS + TECHNICAL_FACTORS + FUNDAMENTAL_FACTORS

# 标准化方法选择
NORMALIZATION_METHODS = {
    # 价格相关的归一化（基于Donchian通道）
    'price_based': ['open', 'high', 'low', 'close'],
    
    # 百分比归一化
    'percentage_based': ['turnover_rate', 'pct_chg'],
    
    # 比率归一化
    'ratio_based': ['pe', 'pb', 'volume_ratio'],
    
    # 标准Z分数归一化
    'zscore_based': TECHNICAL_FACTORS,
    
    # 差分归一化（当日vs前日）
    'diff_based': ['vol', 'amount']
}

# =============================================================================
# 4. 神经网络架构配置
# =============================================================================
# Buy Knowledge RL Agent 配置
BUY_KNOWLEDGE_CONFIG = {
    'state_dim': len(SELECTED_FACTORS),  # 输入维度（因子数量）
    'action_dim': 2,  # 输出维度：[>=10%概率, <10%概率]
    
    # Actor Network架构
    'actor_layers': [len(SELECTED_FACTORS), 128, 64, 32, 2],
    'actor_activation': 'relu',
    'actor_output_activation': 'softmax',
    
    # Critic Network架构  
    'critic_layers': [len(SELECTED_FACTORS), 128, 64, 32, 1],
    'critic_activation': 'relu',
    'critic_output_activation': 'linear',
    
    # 学习率
    'actor_lr': 3e-4,
    'critic_lr': 3e-4,
    
    # 网络初始化
    'weight_init': 'xavier_uniform',
    'bias_init': 'zeros'
}

# Sell Knowledge RL Agent 配置
SELL_KNOWLEDGE_CONFIG = {
    'state_dim': len(SELECTED_FACTORS) + 1,  # +1 for current return since buy
    'action_dim': 2,  # [卖出概率, 持有概率]
    
    # Actor Network架构 
    'actor_layers': [len(SELECTED_FACTORS) + 1, 128, 64, 32, 2],
    'actor_activation': 'relu',
    'actor_output_activation': 'softmax',
    
    # Critic Network架构
    'critic_layers': [len(SELECTED_FACTORS) + 1, 128, 64, 32, 1], 
    'critic_activation': 'relu',
    'critic_output_activation': 'linear',
    
    # 学习率
    'actor_lr': 3e-4,
    'critic_lr': 3e-4,
    
    # 网络初始化
    'weight_init': 'xavier_uniform',
    'bias_init': 'zeros'
}

# =============================================================================
# 5. PPO算法配置
# =============================================================================
PPO_CONFIG = {
    # 学习参数
    'learning_rate': 3e-4,
    'gamma': 0.99,  # 折扣因子
    'gae_lambda': 0.95,  # GAE参数
    'clip_range': 0.2,  # PPO裁剪参数
    'value_coef': 0.5,  # 价值函数损失系数
    'entropy_coef': 0.01,  # 熵正则化系数
    'max_grad_norm': 0.5,  # 梯度裁剪
    
    # 训练参数
    'n_steps': 2048,  # 每次更新的步数
    'batch_size': 64,  # 批大小
    'n_epochs': 10,  # 每次更新的轮数
    'n_envs': 1,  # 并行环境数量
    
    # 经验回放
    'use_experience_replay': False,
    'replay_buffer_size': 100000,
    
    # 网络更新频率
    'target_network_update_freq': 1000,
    'policy_update_freq': 1
}

# =============================================================================
# 6. 训练配置
# =============================================================================
TRAINING_CONFIG = {
    # 训练轮数
    'total_timesteps': 1000000,
    'max_episodes': 10000,
    'max_episode_length': 1000,
    
    # Early Stopping
    'early_stopping_patience': 50,
    'early_stopping_threshold': 1e-4,
    
    # 模型保存
    'save_freq': 10000,  # 每N步保存一次
    'eval_freq': 5000,   # 每N步评估一次
    'log_freq': 100,     # 每N步记录一次
    
    # 验证配置
    'validation_episodes': 100,
    'validation_freq': 20,  # 每N个episode验证一次
    
    # 多进程训练
    'n_workers': 4,
    'use_multiprocessing': True
}

# =============================================================================
# 7. 环境配置
# =============================================================================
ENV_CONFIG = {
    # 交易环境设置
    'initial_balance': 10000.0,  # 初始资金
    'max_positions': 10,  # 最大持仓股票数
    'max_position_pct': 0.1,  # 单只股票最大仓位
    'transaction_fee': 0.001,  # 交易费用
    
    # 数据窗口
    'lookback_window': 60,  # 历史数据回看窗口
    'min_history': 120,  # 最少历史数据量
    
    # 奖励函数设置
    'success_threshold': 0.1,  # 成功阈值10%
    'reward_shaping': True,    # 是否使用奖励塑形
    'reward_scale': 1.0,       # 奖励缩放系数
    
    # Buy Knowledge RL环境特定配置
    'buy_env': {
        'positive_reward': 1.0,    # 正确预测奖励
        'negative_reward': 0.0,    # 错误预测惩罚
        'prediction_window': 5,    # 预测窗口（天）
    },
    
    # Sell Knowledge RL环境特定配置  
    'sell_env': {
        'hold_period': 120,        # 最大持有天数
        'max_reward': 2.0,         # 最大奖励
        'min_reward': -1.0,        # 最小奖励
        'relative_reward': True,   # 使用相对奖励
    }
}

# =============================================================================
# 8. Stop Loss规则配置  
# =============================================================================
STOP_LOSS_CONFIG = {
    # Stop Loss on Dips
    'stop_loss_threshold': -0.1,  # 止损阈值-10%
    
    # Stop Loss on Sideways
    'sideways_days': 20,           # 横盘天数阈值  
    'sideways_return_threshold': 0.1,  # 横盘收益阈值
    'sideways_check_window': 120,  # 检查窗口
    
    # 动态止损
    'use_dynamic_stop_loss': True,
    'trailing_stop_pct': 0.05,    # 跟踪止损百分比
    'volatility_multiplier': 2.0   # 波动率倍数
}

# =============================================================================
# 9. Donchian Channel策略配置
# =============================================================================
DONCHIAN_CONFIG = {
    'upper_window': 20,  # 上轨窗口期
    'lower_window': 20,  # 下轨窗口期  
    'entry_threshold': 0.0,  # 入场阈值
    'exit_threshold': 0.0,   # 出场阈值
    'use_volume_filter': True,  # 是否使用成交量过滤
    'min_volume_ratio': 1.5     # 最小成交量比率
}

# =============================================================================
# 10. 回测与评估配置
# =============================================================================
BACKTEST_CONFIG = {
    # 回测参数
    'initial_capital': 10000.0,
    'benchmark': '000905.SH',  # 中证500作为基准
    'rebalance_freq': 'D',     # 再平衡频率：D日，W周，M月
    
    # 评估指标
    'evaluation_metrics': [
        'total_return', 'annual_return', 'sharpe_ratio', 
        'max_drawdown', 'calmar_ratio', 'sortino_ratio',
        'win_rate', 'profit_factor', 'average_return_per_trade'
    ],
    
    # 风险管理
    'max_single_loss': 0.02,   # 单次最大损失
    'max_daily_loss': 0.05,    # 单日最大损失
    'max_drawdown_limit': 0.2,  # 最大回撤限制
    
    # 交易统计
    'min_holding_period': 1,   # 最小持有天数
    'max_holding_period': 120, # 最大持有天数
    'commission_rate': 0.001   # 佣金费率
}

# =============================================================================
# 11. 日志和监控配置
# =============================================================================
LOGGING_CONFIG = {
    'log_level': 'INFO',
    'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'log_file': os.path.join(LOGS_DIR, f'pro_trader_rl_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
    
    # TensorBoard配置
    'use_tensorboard': True,
    'tensorboard_log_dir': os.path.join(LOGS_DIR, 'tensorboard'),
    
    # 性能监控
    'monitor_performance': True,
    'performance_log_freq': 1000,
    
    # 模型检查点
    'save_checkpoints': True,
    'checkpoint_freq': 5000
}

# =============================================================================
# 12. 实验配置
# =============================================================================
EXPERIMENT_CONFIG = {
    'experiment_name': f'ProTraderRL_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
    'random_seed': 42,
    'reproducible': True,
    
    # A/B测试配置
    'enable_ab_testing': False,
    'ab_test_configs': {
        'baseline': {'learning_rate': 3e-4, 'clip_range': 0.2},
        'variant_1': {'learning_rate': 1e-4, 'clip_range': 0.1},
        'variant_2': {'learning_rate': 5e-4, 'clip_range': 0.3}
    },
    
    # 超参数优化
    'enable_hyperopt': False,
    'hyperopt_trials': 100,
    'hyperopt_params': {
        'learning_rate': (1e-5, 1e-3),
        'clip_range': (0.1, 0.3),
        'gamma': (0.95, 0.99),
        'gae_lambda': (0.9, 0.98)
    }
}

# =============================================================================
# 13. 部署配置
# =============================================================================
DEPLOYMENT_CONFIG = {
    # 模型版本管理
    'model_version': '1.0.0',
    'model_registry_path': 'model_registry',
    
    # 实时交易配置
    'enable_live_trading': False,
    'paper_trading': True,
    'risk_check_interval': 60,  # 秒
    
    # API配置
    'api_config': {
        'host': 'localhost',
        'port': 8000,
        'debug': False,
        'reload': False
    },
    
    # 数据源配置
    'data_source': 'tushare',
    'real_time_data': False,
    'data_update_freq': 300  # 秒
}

# =============================================================================
# 辅助函数
# =============================================================================
def get_factor_config():
    """获取因子配置"""
    return {
        'selected_factors': SELECTED_FACTORS,
        'normalization_methods': NORMALIZATION_METHODS,
        'technical_factors': TECHNICAL_FACTORS,
        'fundamental_factors': FUNDAMENTAL_FACTORS
    }

def get_model_config(model_type='buy'):
    """获取指定模型配置"""
    if model_type.lower() == 'buy':
        return BUY_KNOWLEDGE_CONFIG
    elif model_type.lower() == 'sell':
        return SELL_KNOWLEDGE_CONFIG
    else:
        raise ValueError(f"Unknown model type: {model_type}")

def validate_config():
    """验证配置合理性"""
    errors = []
    
    # 检查路径
    if not os.path.exists(os.path.dirname(PYTHON_ENV_PATH)):
        errors.append(f"Python环境路径不存在: {PYTHON_ENV_PATH}")
    
    # 检查因子数量
    if len(SELECTED_FACTORS) == 0:
        errors.append("未选择任何因子")
    
    # 检查网络配置
    if BUY_KNOWLEDGE_CONFIG['state_dim'] != len(SELECTED_FACTORS):
        errors.append("Buy Knowledge网络输入维度与因子数量不匹配")
    
    # 检查日期配置
    try:
        train_start = datetime.strptime(TRAIN_START_DATE, '%Y%m%d')
        train_end = datetime.strptime(TRAIN_END_DATE, '%Y%m%d')
        if train_start >= train_end:
            errors.append("训练开始日期应早于结束日期")
    except ValueError:
        errors.append("日期格式错误，应为YYYYMMDD")
    
    if errors:
        raise ValueError("配置验证失败:\n" + "\n".join(errors))
    
    return True

# 创建必要目录
def create_directories():
    """创建必要的目录结构"""
    dirs_to_create = [DATA_DIR, MODEL_SAVE_DIR, LOGS_DIR, RESULTS_DIR]
    for directory in dirs_to_create:
        os.makedirs(directory, exist_ok=True)
    
    # 创建子目录
    os.makedirs(os.path.join(MODEL_SAVE_DIR, 'buy_knowledge'), exist_ok=True)
    os.makedirs(os.path.join(MODEL_SAVE_DIR, 'sell_knowledge'), exist_ok=True)
    os.makedirs(os.path.join(LOGS_DIR, 'tensorboard'), exist_ok=True)

if __name__ == "__main__":
    # 验证配置
    try:
        validate_config()
        print("✅ 配置验证通过")
        
        # 创建目录
        create_directories()
        print("✅ 目录结构创建完成")
        
        # 打印配置摘要
        print("\n" + "="*50)
        print("Pro Trader RL 配置摘要")
        print("="*50)
        print(f"选择因子数量: {len(SELECTED_FACTORS)}")
        print(f"训练时间范围: {TRAIN_START_DATE} - {TRAIN_END_DATE}")
        print(f"测试时间范围: {TEST_START_DATE} - {TEST_END_DATE}")
        print(f"Buy Knowledge 网络: {BUY_KNOWLEDGE_CONFIG['actor_layers']}")
        print(f"Sell Knowledge 网络: {SELL_KNOWLEDGE_CONFIG['actor_layers']}")
        print(f"学习率: {PPO_CONFIG['learning_rate']}")
        print(f"初始资金: {ENV_CONFIG['initial_balance']}")
        print(f"最大持仓: {ENV_CONFIG['max_positions']}")
        print("="*50)
        
    except ValueError as e:
        print(f"❌ 配置错误: {e}")
